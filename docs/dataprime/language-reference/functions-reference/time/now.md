---
title: "now"
date: "2024-10-13"
description: "Get the current time as a timestamp"
---

# now - Get the current time as a timestamp

The `now` function gets the current time at query execution time. This has some important features:

* `now` returns a timestamp in nanoseconds if supported by the runtime, otherwise it falls back to milliseconds. 
* `now` is consistent no matter where it is invoked in the query, even multiple invocations.

## Syntax

```
now(): timestamp
```

### Example - Basic usage

Using `now` is a simple function without any arguments:

```dataprime
create now_time from now()
```

### Example - Time since log generation

It is sometimes useful to know exactly how long it has been since a log was written. This can be done using `now`:

```dataprime
create time_since_write from now() - $m.timestamp
```

This will create a field `time_since_write` which is of type `interval`. 


