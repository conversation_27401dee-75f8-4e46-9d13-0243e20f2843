---
title: "TCO Optimizer HTTP API"
date: "2021-08-26"
coverImage: "TCO-Optimizer-ROI.png"
description: "This guide will help you use our TCO Optimizer HTTP API to define, query, and manage your TCO policy overrides, used exclusively for logs."
show_blocks:
  - domain_selector
---

This tutorial demonstrates using our TCO Optimizer HTTP API to define, query, and manage your TCO policy overrides, which are used exclusively for logs.

Visit [this page](../tco-tracing-policy-grpc-api/index.md) to learn how to use our **TCO Tracing gPRC API** to define, query, and manage your [TCO policy criteria](../../../../user-guides/account-management/tco-optimizer/logs/index.md#policy-criteria), used both for spans and logs.

## Base URL

Select the base API endpoint associated with your Coralogix [domain](../../../../user-guides/account-management/account-settings/coralogix-domain/index.md).

| Domain | Base API endpoint |
| --- | --- |
| coralogix.us (Ohio) | https://api.coralogix.us/api/v1/external/tco/ |
| cx498.coralogix.com (Oregon) | https://api.cx498.coralogix.com/api/v1/external/tco/ |
| coralogix.com (Ireland) | https://api.coralogix.com/api/v1/external/tco/ |
| eu2.coralogix.com (Stockholm) | https://api.eu2.coralogix.com/api/v1/external/tco/ |
| coralogix.in (Mumbai) | https://api.app.coralogix.in/api/v1/external/tco/ |
| coralogixsg.com (Singapore) | https://api.coralogixsg.com/api/v1/external/tco/ |
| ap3.coralogix.com (Jakarta) | https://api.ap3.coralogix.com/api/v1/external/tco/ |

## Header

| Key | Value |
| --- | --- |
| Content-Type | application/json |
| Authorization | Bearer `<cx_api_key>` |

### API key
To use the TCO Optimizer API you need to [create](../../../../user-guides/account-management/api-keys/api-keys/index.md) a personal or team API key. It’s recommended to use permission presets, as they are automatically updated with all relevant permissions. Alternatively, you can manually add individual permissions.

| Preset | Action | Description |
| --- | --- | --- |
| TCOPolicies | `LOGS.TCO:READPOLICIES` <br> `LOGS.TCO:UPDATEPOLICIES` <br> `SPANS.TCO:READPOLICIES` <br> `SPANS.TCO:UPDATEPOLICIES` <br> `METRICS.TCO:READPOLICIES` <br> `METRICS.TCO:UPDATEPOLICIES` | View Logs TCO Policies <br> Manage Logs TCO Policies <br> View Tracing TCO Policies <br> Manage Tracing TCO Policies <br> View Metrics TCO Policies <br> Manage Metrics TCO Policies |

## Usage

### Severity options

| Name | Value |
| --- | --- |
| debug | 1 |
| verbose | 2 |
| info | 3 |
| warning | 4 |
| error | 5 |
| critical | 6 |

### Priority options

| Name | Value |
| --- | --- |
| block | block |
| low | low |
| medium | medium |
| high | high |

## TCO override

Configuring a TCO override replaces the TCO policy for the specified application-subsystem-severity combination, including any associated Archive Retention Tags. In these cases, the Archive Retention Tag is overridden and reverts to the `default` tag.

### Impact

This behavior directly impacts S3 lifecycle policies that depend on retention tags. If the tag is overridden to the `default`, lifecycle policies associated with specific tags, like 'Revenue,' may not be applied as intended, potentially resulting in unintended data deletion.

### Prevention

If you manage S3 Lifecycle Policies based on Archive Retention Tags, review and adjust overrides to prevent unintended data deletion.

**Example**

- TCO override is not in use: Retention Tag = `revenue`, Lifecycle Policy transitions data to Glacier after 30 days and deletes it from Glacier after 1 year.

- TCO override is in use: Retention Tag = `default`, Lifecycle Policy expires data after 60 days (if set to `default`).


## Supported endpoints

### Get all policy overrides

**GET** /overrides

**Route example:** https://api.{{ endpoints.domain_only }}/api/v1/external/tco/overrides

**Response example:**

```json
[
    {
        "id": "dd361b69-89c7-11ec-a5ad-0616c20b31c7",
        "name": "default|recommendationservice|INFO",
        "priority": "high",
        "severity": 3,
        "applicationName": "default",
        "subsystemName": "recommendationservice"
    },
    {
        "id": "61d551af-8f96-11ec-8bfb-02dd69f0920d",
        "name": "default|checkoutservice|DEBUG",
        "priority": "high",
        "severity": 1,
        "applicationName": "default",
        "subsystemName": "checkoutservice"
    }
]
```

### Get single policy override by ID

**GET** /overrides/{id}

**Route example:** https://api.{{ endpoints.domain_only }}/api/v1/external/tco/overrides/972f6b98-343c-11ee-ac29-061115d0c307

**Response example:**

```json
{
    "id": "dd361b69-89c7-11ec-a5ad-0616c20b31c7",
    "name": "default|recommendationservice|INFO",
    "priority": "high",
    "severity": 3,
    "applicationName": "default",
    "subsystemName": "recommendationservice"
}
```

### Create single policy override

**POST** /overrides

**Route example:** https://api.{{ endpoints.domain_only }}/api/v1/external/tco/overrides

**Request example:**

```json
{
    "priority": "high",
    "severity": 3,
    "applicationName": "default",
    "subsystemName": "blablabla123"
}
```

**Response example**:

```json
{
    "priority": "high",
    "severity": 3,
    "applicationName": "default",
    "subsystemName": "blablabla123",
    "id": "972f6b98-343c-11ee-ac29-061115d0c307"
}
```

### Create multiple policy overrides

**POST** /overrides/bulk

**Route example:** https://api.{{ endpoints.domain_only }}/api/v1/external/tco/overrides/bulk

**Request example:**

```json
[
    {
        "priority": "high",
        "severity": 3,
        "applicationName": "default",
        "subsystemName": "blablabla1234"
    },
    {
        "priority": "high",
        "severity": 3,
        "applicationName": "default",
        "subsystemName": "blablabla12345"
    }
]
```

**Response example**:

```json
[
    {
        "status": 200,
        "override": {
            "priority": "high",
            "severity": 3,
            "applicationName": "default",
            "subsystemName": "blablabla1234",
            "id": "2c42a7aa-343d-11ee-ac29-061115d0c307"
        }
    },
    {
        "status": 200,
        "override": {
            "priority": "high",
            "severity": 3,
            "applicationName": "default",
            "subsystemName": "blablabla12345",
            "id": "2c53d05f-343d-11ee-ac29-061115d0c307"
        }
    }
]
```

### Update multiple policy overrides

**PUT** /overrides/bulk

**Route example:** https://api.{{ endpoints.domain_only }}/api/v1/external/tco/overrides/bulk

**Request example:**

```json
[
    {
        "id": "2c42a7aa-343d-11ee-ac29-061115d0c307",
        "name": "default|blablabla1234|INFO",
        "priority": "high",
        "severity": 3,
        "applicationName": "default",
        "subsystemName": "blablabla1234"
    },
    {
        "id": "2c53d05f-343d-11ee-ac29-061115d0c307",
        "name": "default|blablabla12345|INFO",
        "priority": "high",
        "severity": 3,
        "applicationName": "default",
        "subsystemName": "blablabla12345"
    }
]
```

**Response example**:

```json
[
    {
        "status": 200,
        "override": {
            "name": "default|blablabla1234|INFO",
            "priority": "high",
            "severity": 3,
            "applicationName": "default",
            "subsystemName": "blablabla1234",
            "id": "2c42a7aa-343d-11ee-ac29-061115d0c307"
        }
    },
    {
        "status": 200,
        "override": {
            "name": "default|blablabla12345|INFO",
            "priority": "high",
            "severity": 3,
            "applicationName": "default",
            "subsystemName": "blablabla12345",
            "id": "2c53d05f-343d-11ee-ac29-061115d0c307"
        }
    }
]
```

### Delete single policy override

**DELETE** /overrides/{id}

**Route example:** https://api.{{ endpoints.domain_only }}/api/v1/external/tco/overrides/2c53d05f-343d-11ee-ac29-061115d0c307

**Response example:**

```json
{
    "id": "2c53d05f-343d-11ee-ac29-061115d0c307"
}
```

### Delete multiple policy overrides

**DELETE** /overrides/bulk

**Route example:** https://api.{{ endpoints.domain_only }}/api/v1/external/tco/overrides/bulk

**Request example:**

```json
[
    {
        "id": "2c42a7aa-343d-11ee-ac29-061115d0c307"
    }
]
```

**Response example**:

```json
[
    {
        "status": 200,
        "override": {
            "id": "2c42a7aa-343d-11ee-ac29-061115d0c307"
        }
    }
]
```

### Get all TCO policies

**GET** /policies

**Route example:** https://api.{{ endpoints.domain_only }}/api/v1/external/tco/policies

**Request example:**

```
curl --location 'https://api.{{ endpoints.domain_only }}/api/v1/external/tco/policies' \
--header 'Authorization: Bearer xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx'
```

### Create TCO policy

**POST** /policies

**Route example:** https://api.{{ endpoints.domain_only }}/api/v1/external/tco/policies

**Request example:**

```
curl --location 'https://api.{{ endpoints.domain_only }}/api/v1/external/tco/policies' \
--header 'Authorization: Bearer xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx' \
--header 'Content-Type: application/json' \
--data '{
    "name": "Policy block subsystem",
    "priority": "block",
    "severities": [],
    "applicationName": {},
    "subsystemName": {
        "type": "Is",
        "rule": [
            "system_logs",
            "lambda_logs"
        ]
    }
}'
```

### Reorder policies
Sets the order of policies in the provided order. The list must contain the list of all the policies IDs in the expected new order.

**POST** /policies/reorder

**Route example:** https://api.{{ endpoints.domain_only }}/api/v1/external/tco/policies/reorder

**Request example:**

```
curl --location --request PUT 'https://api.{{ endpoints.domain_only }}/api/v1/external/tco/policies/reorder' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx' \
--data '[
    "eed91808-1094-4d07-adab-17855b449888",
    "95cfa816-354a-412c-80f5-378ce33a4454"
]'
```

### Delete TCO policy

**DELETE** /policies/{tco_policy_id}

**Route example:** https://api.{{ endpoints.domain_only }}/api/v1/external/tco/policies/90503f12-c291-4bbf-b437-5022f39d258d

**Request example:**

```
curl --location --request DELETE 'https://api.{{ endpoints.domain_only }}/api/v1/external/tco/policies/90503f12-c291-4bbf-b437-5022f39d258d' \
--header 'Authorization: Bearer xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx'
```

## Additional resources
| | |
| --- | --- |
| Documentation | [TCO Optimizer](../../../../user-guides/account-management/tco-optimizer/logs/index.md)<br/>[TCO Tracing gPRC API](../tco-tracing-policy-grpc-api/index.md) |

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email at [<EMAIL>](mailto:<EMAIL>).
