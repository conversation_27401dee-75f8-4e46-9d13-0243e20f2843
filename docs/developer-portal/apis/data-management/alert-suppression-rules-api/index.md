---
title: "Alert Suppression Rules API"
date: "2024-03-04"
description: "Alert Suppression Rules allow you to automatically mute alerts according to your specific parameters. You can set what to suppress (what group-by keys), when to suppress (specific times and dates, recurring times and dates, one-time suppressions), and which alerts to suppress during those times."
show_blocks:
  - domain_selector
---

## Overview

This document outlines the Alert Suppression Rules API. Alert Suppression Rules allow you to mute alerts according to your specific parameters automatically. You can set what to suppress (what group by keys), when to suppress (specific times and dates, recurring times and dates, one-time suppressions), and which alerts to suppress during those times.

### Prerequisites

Before you begin, please make sure you have the following:

- [API Key for Alerts, Rules & Tags](../../../../user-guides/account-management/api-keys/api-keys/index.md) to successfully authenticate

- [Management API Endpoint](../../../../integrations/coralogix-endpoints.md#management) that corresponds with your Coralogix domain

- Administrator permissions to manage your services

## Authentication

Coralogix API uses API keys to authenticate requests. You can view and [manage your API keys](../../../../user-guides/account-management/api-keys/api-keys/index.md) from the Data Flow tab in Coralogix. You need to use this API key in the Authorization request header to successfully connect.

Then, use one of our designated [Management endpoints](../../../../integrations/coralogix-endpoints.md#management) to structure your header.

For the Alert Suppression Rules API, the service name will be `AlertSchedulerRuleService`, and with its namespace becomes `com.coralogixapis.alerting.alert_scheduler_rule_protobuf.v1.AlertSchedulerRuleService`. 

The request header base should look like this:

``` bash
grpcurl -H "Authorization: Bearer <cx_api_key>" -d @ {{ endpoints.management }} com.coralogixapis.alerting.alert_scheduler_rule_protobuf.v1.AlertSchedulerRuleService
```

The complete request header should also contain a specific API endpoint. For example, to create a rule use the following:

```bash
grpcurl -H "Authorization: Bearer <cx_api_key>" -d @ {{ endpoints.management }} com.coralogixapis.alerting.alert_scheduler_rule_protobuf.v1.AlertSchedulerRuleService/CreateAlertSchedulerRule
```

## API Endpoints

### AlertSchedulerRuleService

| Method Name | Description |
| --- | --- |
| GetAlertSchedulerRule | Retrieves an alert suppression rule based on the provided rule ID. |
| CreateAlertSchedulerRule | Creates a new alert suppression rule according to the provided parameters. |
| UpdateAlertSchedulerRule | Updates an existing alert suppression rule with new settings and configurations. |
| DeleteAlertSchedulerRule | Deletes a specified alert suppression rule, removing it from the system. |
| GetBulkAlertSchedulerRule | Retrieves multiple alert suppression rules in bulk, potentially filtered by various criteria. |
| CreateBulkAlertSchedulerRule | Creates multiple alert suppression rules in bulk, based on the provided list of rule creation requests. |
| UpdateBulkAlertSchedulerRule | Updates multiple existing alert suppression rules with new settings and configurations in bulk. |

### GetAlertSchedulerRule

### Get Alert Scheduler Rule Request

| Field | Type | Label | Description |
| --- | --- | --- | --- |
| alert_scheduler_rule_id | string |  | The rule ID. |

### GetAlertSchedulerRuleResponse

| Field | Type | Label | Description |
| --- | --- | --- | --- |
| alert_scheduler_rule | AlertSchedulerRule |  | Metadata from the alert rule. |

### CreateAlertSchedulerRule

### CreateAlertSchedulerRuleRequest

| Field | Type | Label | Description |
| --- | --- | --- | --- |
| alert_scheduler_rule | AlertSchedulerRule |  | Metadata from the alert rule |

### CreateAlertSchedulerRuleResponse

| Field | Type | Label | Description |
| --- | --- | --- | --- |
| alert_scheduler_rule | AlertSchedulerRule |  | Metadata from the alert rule |

### UpdateAlertSchedulerRule

### UpdateAlertSchedulerRuleRequest

| Field | Type | Label | Description |
| --- | --- | --- | --- |
| alert_scheduler_rule | AlertSchedulerRule |  | Metadata from the alert rule |

### UpdateAlertSchedulerRuleResponse

| Field | Type | Label | Description |
| --- | --- | --- | --- |
| alert_scheduler_rule | AlertSchedulerRule |  | Metadata from the alert rule |

### DeleteAlertSchedulerRule

### DeleteAlertSchedulerRuleRequest

| Field | Type | Label | Description |
| --- | --- | --- | --- |
| alert_scheduler_rule_id | string |  | The rule ID. |

### DeleteAlertSchedulerRuleResponse

| Field | Type | Label | Description |
| --- | --- | --- | --- |
|  |  |  |  |

### GetBulkAlertSchedulerRule

### GetBulkAlertSchedulerRuleRequest

| Field | Type | Label | Description |
| --- | --- | --- | --- |
| active_timeframe | ActiveTimeframe |  |  |
| enabled | bool | optional |  |
| alert_scheduler_rules_ids | FilterByAlertSchedulerRuleIds |  |  |
| next_page_token | string | optional |  |

### GetBulkAlertSchedulerRuleResponse

| Field | Type | Label | Description |
| --- | --- | --- | --- |
| alert_scheduler_rules | AlertSchedulerRuleWithActiveTimeframe | repeated |  |
| next_page_token | string |  |  |

### CreateBulkAlertSchedulerRule

### CreateBulkAlertSchedulerRuleRequest

| Field | Type | Label | Description |
| --- | --- | --- | --- |
| create_alert_scheduler_rule_requests | CreateAlertSchedulerRuleRequest | repeated |  |

### CreateBulkAlertSchedulerRuleResponse

| Field | Type | Label | Description |
| --- | --- | --- | --- |
| create_suppression_responses | CreateAlertSchedulerRuleResponse | repeated |  |

### UpdateBulkAlertSchedulerRule

### UpdateBulkAlertSchedulerRuleRequest

| Field | Type | Label | Description |
| --- | --- | --- | --- |
| update_alert_scheduler_rule_requests | UpdateAlertSchedulerRuleRequest | repeated |  |

### UpdateBulkAlertSchedulerRuleResponse

| Field | Type | Label | Description |
| --- | --- | --- | --- |
| update_suppression_responses | UpdateAlertSchedulerRuleResponse | repeated |  |

### DeleteBulkAlertSchedulerRule

### DeleteBulkAlertSchedulerRuleRequest

| Field | Type | Label | Description |
| --- | --- | --- | --- |
| delete_alert_scheduler_rule_requests | DeleteAlertSchedulerRuleRequest | repeated |  |

### DeleteBulkAlertSchedulerRuleResponse

| Field | Type | Label | Description |
| --- | --- | --- | --- |
|  |  |  |  |

### AlertSchedulerRule

| Field | Type | Label | Description |
| --- | --- | --- | --- |
| unique_identifier | string | optional | Rule unique_identifier: The rule id. |
| id | string | optional | Rule id: The rule version id. |
| name | string |  | Rule name. |
| description | string | optional | Rule description. |
| meta_labels | MetaLabel | repeated | Rule meta_labels: Rule tags over the system. |
| filter | Filter |  | Rule filter: The rule filter definition over alert-events. |
| schedule | Schedule |  | Rule schedule: The schedule time definition, how often the rule will be active. |
| enabled | bool |  | Rule enabled: The rule activation mode. |
| created_at | string | optional | Rule created_at: The date-time when the rule was created. |
| updated_at | string | optional | Rule updated_at: The date-time when the rule was updated. |

### AlertSchedulerRuleWithActiveTimeframe

| Field | Type | Label | Description |
| --- | --- | --- | --- |
| alert_scheduler_rule | AlertSchedulerRule |  |  |
| next_active_timeframes | ActiveTimeframe | repeated |  |

### ActiveTimeframe

| Field | Type | Label | Description |
| --- | --- | --- | --- |
| start_time | string |  | Timeframe start time: The point in the time(date-time) when the rule will start to be active. |
| end_time | string |  | Timeframe end time: The point in the time(date-time) when the rule will finish to be active. |
| timezone | string |  | Timeframe timezone: The rule will be active according to a specific timezone. |

### Duration

| Field | Type | Label | Description |
| --- | --- | --- | --- |
| for_over | int32 |  | Duration for_over: The duration interval. |
| frequency | DurationFrequency |  | Duration frequency: The duration frequency types (minute hour or day). |

### Timeframe

| Field | Type | Label | Description |
| --- | --- | --- | --- |
| start_time | string |  | Timeframe start time: The point in the time(date-time) when the rule will start to be active. |
| end_time | string |  | Timeframe end time: The point in the time(date-time) when the rule will finish to be active. |
| duration | Duration |  | Timeframe duration: The duration interval of the rule activation. |
| timezone | string |  | Timeframe timezone: The rule will be active according to a specific timezone. |

!!! Note
    Avoid including time zone indicators (like 'Z' for UTC) in the `start_time` and `end_time` fields. For example, use `2025-05-21T22:41:26` instead of `2025-05-21T22:41:26Z`.

### DurationFrequency

| Name | Number |
| --- | --- |
| DURATION_FREQUENCY_UNSPECIFIED | 0 |
| DURATION_FREQUENCY_MINUTE | 1 |
| DURATION_FREQUENCY_HOUR | 2 |
| DURATION_FREQUENCY_DAY | 3 |

### Recurring

| Field | Type | Label | Description |
| --- | --- | --- | --- |
| always | Recurring.Always |  |  |
| dynamic | Recurring.Dynamic |  |  |

### Recurring.Always

| Field | Type | Label | Description |
| --- | --- | --- | --- |
|  |  |  |  |

### Recurring.Dynamic

| Field | Type | Label | Description |
| --- | --- | --- | --- |
| repeat_every | int32 |  | Recurring Dynamic repeat_every: The rule will be activated in a recurring mode according to the interval. |
| daily | Daily |  |  |
| weekly | Weekly |  |  |
| monthly | Monthly |  |  |
| timeframe | Timeframe |  | Recurring Dynamic timeframe: The rule will be activated in a recurring mode according to the specific timeframe. |
| termination_date | string | optional | Recurring Dynamic termination_date: The rule will be terminated according to termination_date. |

### Schedule

| Field | Type | Label | Description |
| --- | --- | --- | --- |
| schedule_operation | ScheduleOperation |  | Rule schedule_operation: The rule operation mode (mute/active). |
| one_time | OneTime |  | Schedule one_time: The scheduling definition will activate the rule for a specific timeframe. |
| recurring | Recurring |  | Schedule recurring: The scheduling definition will activate the rule for a recurring timeframe. |

### Daily

| Field | Type | Label | Description |
| --- | --- | --- | --- |
|  |  |  |  |

### Weekly

| Field | Type | Label | Description |
| --- | --- | --- | --- |
| days_of_week | int32 | repeated | Dynamic Weekly days_of_week: The rule will be activated at weekly intervals on specific days in a week. |

### Monthly

| Field | Type | Label | Description |
| --- | --- | --- | --- |
| days_of_month | int32 | repeated | Dynamic Monthly days_of_month: The rule will be activated at monthly intervals on specific days in a month. |

### OneTime

| Field | Type | Label | Description |
| --- | --- | --- | --- |
| timeframe | Timeframe |  |  |

### ScheduleOperation

| Name | Number | Description |
| --- | --- | --- |
| SCHEDULE_OPERATION_UNSPECIFIED | 0 |  |
| SCHEDULE_OPERATION_MUTE | 1 |  |
| SCHEDULE_OPERATION_ACTIVATE | 2 |  |

### FilterByAlertSchedulerRuleIds

| Field | Type | Label | Description |
| --- | --- | --- | --- |
| alert_scheduler_ids | AlertSchedulerRuleIds |  |  |
| alert_scheduler_version_ids | AlertSchedulerRuleVersionIds |  |  |

### AlertSchedulerRuleIds

| Field | Type | Label | Description |
| --- | --- | --- | --- |
| alert_scheduler_rule_ids | string | repeated |  |

### AlertSchedulerRuleVersionIds

| Field | Type | Label | Description |
| --- | --- | --- | --- |
| alert_scheduler_rule_version_ids | string | repeated |  |

### Filter

| Field | Type | Label | Description |
| --- | --- | --- | --- |
| what_expression | string |  | Filter what_expression: dataprime expression that filter the alerts group by values. |
| alert_meta_labels | MetaLabels |  | Filter alert_meta_labels: filter alerts by meta labels tagging. |
| alert_unique_ids | AlertUniqueIds |  | Filter alert_unique_ids: filter specific alerts (when alert_unique_ids is empty meaning it wil filter all alerts). |

### AlertUniqueIds

| Field | Type | Label | Description |
| --- | --- | --- | --- |
| value | string | repeated |  |

### MetaLabels

| Field | Type | Label | Description |
| --- | --- | --- | --- |
| value | MetaLabel | repeated |  |

### MetaLabel

| Field | Type | Label | Description |
| --- | --- | --- | --- |
| id | string | optional | MetaLabel id: The meta label id |
| key | string |  | MetaLabel key: The meta label key |
| value | string | optional | MetaLabel value: The meta label value |
