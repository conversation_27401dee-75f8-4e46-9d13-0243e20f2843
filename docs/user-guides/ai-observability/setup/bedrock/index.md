---
title: "Amazon Bedrock"
date: "2024-02-04"
description: "Through integrations with Bedrock, Coralogix delivers end-to-end visibility into AI workloads, supporting proactive issue detection and efficient performance tuning."
---

# Amazon Bedrock

Coralogix's AI Observability integrations enable organizations to gain deep insight into their AI applications, helping them monitor, analyze, and optimize performance across the stack. Through integrations with Amazon Bedrock, Coralogix delivers end-to-end visibility into AI workloads, supporting proactive issue detection and efficient performance tuning.

## Overview

This library offers customized [OpenTelemetry instrumentation](https://github.com/open-telemetry/opentelemetry-python-contrib/) for Bedrock SDK, optimized to support large language model (LLM) application development with streamlined integration, detailed production tracing, and effective debugging capabilities.

## Requirements

- Python version 3.8 and above.
- Coralogix [API keys](../../account-management/api-keys/api-keys/index.md).

## Installation

Run the following command.

```bash
pip install llm-tracekit[bedrock]
```

## Authentication

Authentication data is passed during OTel Span Exporter definition:

1. Select the [endpoint](../../../integrations/coralogix-endpoints.md) associated with your Coralogix [domain](../../account-management/account-settings/coralogix-domain/index.md) .
2. Use your [customized API key](../../account-management/api-keys/api-keys/index.md) in the authorization request header.
3. Provide the [application and subsystem names](../../account-management/account-settings/application-and-subsystem-names/index.md).

```python
from llm_tracekit import setup_export_to_coralogix

setup_export_to_coralogix(
    coralogix_token=<your_coralogix_token>,
    coralogix_endpoint=<your_coralogix_endpoint>,
    service_name="ai-service",
    application_name="ai-application",
    subsystem_name="ai-subsystem",
    capture_content=True,
)
```

!!! note

    All of the authentication parameters can also be provided through environment variables (`CX_TOKEN`, `CX_ENDPOINT`, etc.).

## Usage

This section describes how to set up instrumentation for Amazon Bedrock.

### Set up tracing

**Automatic**

Use the `setup_export_to_coralogix` function to set up tracing and export traces to Coralogix. See the code snippet in the [Authentication](#authentication) section. 

**Manual**

Alternatively, you can set up tracing manually.

```python
from opentelemetry import trace
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
from opentelemetry.sdk.resources import SERVICE_NAME, Resource
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import SimpleSpanProcessor

tracer_provider = TracerProvider(
    resource=Resource.create({SERVICE_NAME: "ai-service"}),
)
exporter = OTLPSpanExporter()
span_processor = SimpleSpanProcessor(exporter)
tracer_provider.add_span_processor(span_processor)
trace.set_tracer_provider(tracer_provider)
```

### Instrument

To instrument all clients, call the `instrument` method.

```python
from llm_tracekit import BedrockInstrumentor

BedrockInstrumentor().instrument()
```

### Uninstrument

To uninstrument clients, call the `uninstrument` method.

```python
BedrockInstrumentor().uninstrument() 
```

### Full example

```python
from llm_tracekit import BedrockInstrumentor, setup_export_to_coralogix
import boto3

# Optional: Configure sending spans to Coralogix
# Reads Coralogix connection details from the following environment variables:
# - CX_TOKEN
# - CX_ENDPOINT
setup_export_to_coralogix(
    service_name="ai-service",
    application_name="ai-application",
    subsystem_name="ai-subsystem",
    capture_content=True,
)

# Activate instrumentation
BedrockInstrumentor().instrument()

# Example Bedrock Usage
client = boto3.client("bedrock-runtime")
result = client.converse(
    modelId="anthropic.claude-3-5-sonnet-20240620-v1:0",
    system=[{"text": "you are a helpful assistant"}],
    messages=[{"role": "user", "content": [{"text": "Write a short poem on open telemetry."}]}],
    inferenceConfig={
        "maxTokens": 300,
        "temperature": 0,
        "topP": 1,
    }
)
```

### Enable message content capture

By default, message content, such as the contents of the prompt, completion, function arguments and return values, are not captured. To capture message content as span attributes, you can either:

- Pass `capture_content=True` when calling `setup_export_to_coralogix`, or

- Set the environment variable `OTEL_INSTRUMENTATION_GENAI_CAPTURE_MESSAGE_CONTENT=true`.

Most Coralogix AI evaluations require message contents to function properly, so enabling message capture is strongly recommended.

### Key difference from OpenTelemetry

User prompts and model responses are captured as span attributes instead of log events, as detailed below.

## Semantic conventions

| Attribute | Type | Description | Example |
| --- | --- | --- | --- |
| `gen_ai.prompt.<message_number>.role` | string | Role of message author for user message  <message_number> | `system`, `user`, `assistant`, `tool` |
| `gen_ai.prompt.<message_number>.content` | string | Contents of user message <message_number> | `What's the weather in Paris?` |
| `gen_ai.prompt.<message_number>.tool_calls.<tool_call_number>.id` | string | ID of tool call in user message  <message_number> | `call_O8NOz8VlxosSASEsOY7LDUcP` |
| `gen_ai.prompt.<message_number>.tool_calls.<tool_call_number>.type` | string | Type of tool call in user message <message_number> | `function` |
| `gen_ai.prompt.<message_number>.tool_calls.<tool_call_number>.function.name` | string | The name of the function used in tool call within user message  <message_number> | `get_current_weather` |
| `gen_ai.prompt.<message_number>.tool_calls.<tool_call_number>.function.arguments` | string | Arguments passed to the function used in tool call within user message <message_number> | `{"location": "Seattle, WA"}` |
| `gen_ai.prompt.<message_number>.tool_call_id` | string | Tool call ID in user message <message_number> | `call_mszuSIzqtI65i1wAUOE8w5H4` |
| `gen_ai.completion.<choice_number>.role` | string | Role of message author for choice <choice_number>  in model response | `assistant` |
| `gen_ai.completion.<choice_number>.finish_reason` | string | Finish reason for choice <choice_number>  in model response | `stop`, `tool_calls`, `error` |
| `gen_ai.completion.<choice_number>.content` | string | Contents of choice <choice_number>  in model response | `The weather in Paris is rainy and overcast, with temperatures around 57°F` |
| `gen_ai.completion.<choice_number>.tool_calls.<tool_call_number >.id` | string | ID of tool call in choice <choice_number>  | `call_O8NOz8VlxosSASEsOY7LDUcP` |
| `gen_ai.completion.<choice_number>.tool_calls.<tool_call_number >.type` | string | Type of tool call in choice <choice_number>  | `function` |
| `gen_ai.completion.<choice_number>.tool_calls.<tool_call_number >.function.name` | string | The name of the function used in tool call within choice <choice_number>  | `get_current_weather` |
| `gen_ai.completion.<choice_number>.tool_calls.<tool_call_number >.function.arguments` | string | Arguments passed to the function used in tool call within choice <choice_number>  | `{"location": "Seattle, WA"}` |

### Bedrock-specific attributes

| Attribute | Type | Description | Example |
| --- | --- | --- | --- |
| `gen_ai.bedrock.agent_alias.id` | string | The ID of the agent-alias in an `invoke_agent` call | `<EMAIL>` |
| `gen_ai.bedrock.request.tools.<tool_number>.function.name` | string | The name of the function to use in tool calls | `get_current_weather` | 
| `gen_ai.bedrock.request.tools.<tool_number>.function.description` | string | Description of the function | `Get the current weather in a given location` | 
| `gen_ai.bedrock.request.tools.<tool_number>.function.parameters` | string | JSON describing the schema of the function parameters | `{"type": "object", "properties": {"location": {"type": "string", "description": "The city and state, e.g. San Francisco, CA"}, "unit": {"type": "string", "enum": ["celsius", "fahrenheit"]}}, "required": ["location"]}` | 

## Supported endpoints

The library supports the following endpoints:

- `invoke_model` – Compatible with LLaMA and Anthropic Claude for standard model invocation.

- `invoke_model_with_response_stream` – Enables streaming responses from LLaMA and Anthropic Claude.

- `converse` – Facilitates stateful interactions with LLMs.

- `converse_stream` – Supports streaming for stateful conversation sessions.

- `invoke_agent` - Executes agent-based workflows.
