---
title: "Query URLs"
date: "2023-12-31"
description: "Query URLs offer a lightweight and flexible way to run log queries via a direct link. Designed for precision and automation, they support defining the query, timeframe, syntax, and other parameters directly in the URL."
---

# Query URLs

## Overview

**Query URLs** offer a lightweight and flexible way to run log queries via a direct link. Designed for precision and automation, they support defining the query, timeframe, syntax, and other parameters directly in the URL. While they can run independently, they can also be combined with saved views—augmenting results with predefined layouts, filters, and widgets for added context.

!!! Note
    Unlike Query URLs, [Shared URLs](../shared-urls/index.md) are built for UI-based collaboration. They retain the full context of a custom view—including layout, widgets, filters, and query parameters—so teams can access and interpret data consistently.

## Use cases

This feature supports two primary use cases:

- **Static query URL** - Use these to construct a query entirely through URL parameters—ideal for alerting systems, scripts, and integrations where you want predictable, repeatable query behavior. These URLs do not depend on saved views or UI state. The default view of the receiver will be retained, altered by the custom query parameters.
    
    ```
    https://<team>.<domain>/#/query-new/<data_pipeline>?querySyntax=<query_language>&time=<time_query>&query=<query>&permalink=true
    ```
    
    For example:
    
    ```
    https://<team>.coralogix.com/#/query-new/archive-logs?&querySyntax=lucene&time=from:now-1h,to:now&query=_exists_:kubernetes.pod.ip&permalink=true
    ```
    
- **Custom query URL** - Combine a view (with its layout, filters, and widgets) with dynamic query parameters such as a new query or timeframe. This enables templated dashboards or reusable UI configurations tailored to different runtime contexts.
    
    ```
    https://<team>.<domain>/#/query-new/logs?viewId=<saved_view_id>&querySyntax=<query_language>&time=<time_query>&query=<query>&permalink=true
    ```
    
    For example:
    
    ```
    https://<team>.coralogix.com/#/query-new/logs?viewId=83871&querySyntax=lucene&time=from:2025-03-09T07:15:07.611Z,to:2025-03-09T09:15:07.611Z&query=cx_rum.network_request_context.status_code:500&permalink=true
    ```
    

## Prerequisites

To access the results of a query URL, users must be logged into a Coralogix account with the same domain as specified in the URL.

## Data pipeline

The URL path determines which [data pipeline](../../../../user-guides/account-management/tco-optimizer/logs/index.md#tco-data-pipelines) is queried:

| Path | Data queried |
| --- | --- |
| `/query-new/logs` | Queries only [Frequent Search](../../../../user-guides/account-management/tco-optimizer/logs/#frequent-search-data-high-priority) (high-priority) indexed logs |
| `/query-new/archive-logs` | Queries all logs, including Frequent Search, Monitoring, and Compliance data |

!!! Note
    When using query URLs to query Frequent Search data, be aware that results may vary over time, as this data is retained for a limited period. Use `archive_logs` to query data from all pipelines if long term data access is required.

## Types of static views

Static query URLs can originate from different sources, each with its own behavior, retention period, and level of context. Some are generated ad hoc from the UI, while others are based on saved views that provide persistent structure and layout. The table below outlines the key differences between each type.

| **View type** | **URL source** | **Lifespan** | **Notes** |
| --- | --- | --- | --- |
| **Unsaved view** | Copied from browser address bar | 2 weeks | URL `id` updates with every query or layout change |
| **Unsaved view** | Generated via Share URL button | 2 months (auto-renewed) | Lifetime extends by 2 months each time the link is accessed |
| **Saved view (public)** | Saved and marked public | Permanent | Uses `viewId`; Edits create a new, unsaved view |
| **Saved view (private)** | Saved but not shared publicly | Permanent (private) | When sharing a private view, the URL will open as an unsaved view with the same parameters and UI layout. |

Learn more about view types with [Create & Manage Saved Views](../../../../user-guides/monitoring-and-insights/explore-screen/create-and-manage-saved-views/index.md).

## Key URL parameters

| Parameter | Description |
| --- | --- |
| `id` | **Unsaved view identifier**. 2-week lifespan (browser copy) or 2-month lifespan (generated using [**Share URL**](../shared-urls/index.md)). |
| `viewId` | Permanent **saved view identifier**. Used for public and private shared saved views. |
| `query` | The query string. Must be URL-encoded. Supports Lucene or DataPrime syntax. |
| `querySyntax` | Indicates the query language. Accepted values: `lucene`, `dataprime`. **Required** when using `query`. |
| `time` | Time range for the query. Format: `from:<ISO-timestamp>,to:<ISO-timestamp>` or relative (`now-15m`). |
| `permalink` | `permalink=true` is **required** only when appending `query` and/or modifying `time`. |
| `page` | Specify a specific page to navigate to in the view. |

!!! Note
    DataPrime supports both standard log searches and advanced query features like grouping and aggregation. Lucene can also be used for simple search queries, but not required.


### Timeframe examples

| **Format** | **Type** | **Example** |
| --- | --- | --- |
| Relative (ISO) | Now to 1 hour ago | `time=from:now-1h,to:now` |
| Absolute (ISO) | Specific timestamp | `time=from:2025-03-09T07:15:07.611Z,to:2025-03-09T09:15:07.611Z` |
| Absolute (Unix) | Timestamp in ms | `time=from:1743067939000,to:1745746339000` |

### Relative time values

| **Unit** | **Examples** |
| --- | --- |
| Minutes | `1m`, `2m`, `5m`, `15m`, `30m` |
| Hours | `1h`, `2h`, `6h`, `12h`, `24h` |
| Days | `2d`, `3d`, `5d`, `7d` |

## URL structure examples

### Unsaved views

```
https://<team>.<domain>/#/query-new/archive-logs?id=qQHw5igzFcmiafUsnp84W&page=0&querySyntax=dataprime

# After modifying any attribute (query, time), the ID is regenerated and a new, unsaved view is created
https://<team>.<domain>/#/query-new/archive-logs?id=6m7cMjbsOVrQcbTzTJ9zT&page=0&querySyntax=dataprime

# Shared URL with updated time range
https://<team>.<domain>/#/query-new/archive-logs?id=6m7cMjbsOVrQcbTzTJ9zT&time=from:now-30m,to:now&page=0&permalink=true
```

### Query override example

To view custom query results as a part of an existing view:

```
# Appending query, querySyntax, and permalink
https://<team>.<domain>/#/query-new/archive-logs?id=6m7cMjbsOVrQcbTzTJ9zT&page=0&querySyntax=dataprime&query=source logs | limit 10&permalink=true
```

## Query syntax-switch examples

You can change the query syntax used in the query URL by modifying the `querySyntax` parameter:

```
# Switch to DataPrime syntax (no query override)
https://<team>.<domain>/#/query-new/logs?id=abc123&querySyntax=dataprime&permalink=true

# Switch to Lucene syntax
https://<team>.<domain>/#/query-new/archive-logs?id=abc123&querySyntax=lucene&permalink=true
```

This change updates the query editor UI mode and determines how the query string is parsed.

## Example URLs

```
# Lucene with absolute time
https://<team>.coralogix.com/#/query-new/logs?viewId=83871&querySyntax=lucene&time=from:2025-03-09T07:15:07.611Z,to:2025-03-09T09:15:07.611Z&query=cx_rum.network_request_context.status_code:500&permalink=true

# Lucene with relative time and field existence
https://<team>.coralogix.com/#/query-new/logs?id=en3EQgqHnMSvyJRhgzmSk&querySyntax=lucene&time=from:now-1h,to:now&query=_exists_:kubernetes.pod.ip&permalink=true

# DataPrime with filter
https://<team>.coralogix.com/#/query-new/archive-logs?id=K1hXTPgjaMoAUpZrgwU2m&querySyntax=dataprime&time=from:now-15m,to:now&query=source%20logs%20%7C%20filter%20$m.severity%20==%20ERROR&permalink=true

# DataPrime with groupby aggregation
https://<team>.coralogix.com/#/query-new/archive-logs?id=K1hXTPgjaMoAUpZrgwU2m&querySyntax=dataprime&time=from:now-2d,to:now&query=source%20logs%20%7C%20groupby%20cloud.region%20aggregate%20sum(if($d.responseStatus.code%20==%20500,%201,%200))%20as%20error_count&permalink=true
```

For help with query syntax, see:

- [Lucene Query Reference](https://lucene.apache.org/core/2_9_4/queryparsersyntax.html)
- [DataPrime Reference Guide](../../../../dataprime/introduction/welcome-to-the-dataprime-reference/index.md)

## Support

**Need help?** 

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up. 

Feel free to reach out to us **via our in-app chat** or by sending us an email to [<EMAIL>](mailto:<EMAIL>).