---
title: "Transactions"
date: "2024-01-23"
description: "Purpose-built for microservices-based environments, the Coralogix transactions feature allows you to rapidly investigate the radius of the impact of different services over time and troubleshoot issues immediately as part of Application Performance Monitoring."
---

Purpose-built for microservices-based environments, the Coralogix **Transactions** feature allows you to rapidly investigate the radius of the impact of different services over time and troubleshoot issues immediately as part of [Application Performance Monitoring](../../getting-started/introduction-to-apm/index.md).

To enjoy the feature, contact us via our in-app chat or by emailing [<EMAIL>](mailto:<EMAIL>).

## Overview

Use transactions to:

- Investigate the performance of each transaction by breaking it down into its constituent operations.

- Gain a granular understanding of how each segment, a collection of related operations, affects the performance of the entire transaction over time.

- Rapidly identify and troubleshoot the segments causing performance issues over time.

## Concepts

A user visits an e-commerce website and clicks on a product to view its details. This action initiates a **transaction**. The transaction begins when the user's request reaches a service and ends when the service sends back the product details as a response. During this transaction, several operations occur: the service might query a database to retrieve the product information, call external services for current pricing, and execute internal logic to prepare the content for display. Each of these **operations** represents a **segment**. The transaction breakdown shows how these segments collectively contribute to the request's overall response time and performance.

### Transaction

Simply put, a **transaction** denotes a singular logical unit of work in a software application. More precisely, it encompasses the function and method calls constituting that unit of work. Each transaction consists of a root span, an operation that serves as its entry point and triggers all other related operations.

### Operation

Every transaction may contain hundreds, if not thousands of spans, grouped per operation name. An **operation** is a logical entity consolidating all spans with the same action type.

### Segment

A **segment** is a collection of related operations, the constituent functions that break down the various **service operations** into a transaction and collectively structure its performance. Examples include external service calls and database calls. Each segment provides insights into its performance and how it affects the transaction over time.

Coralogix’s APM captures and monitors the performance metrics associated with each segment, making up the transaction. This information helps identify bottlenecks, optimize code, and ensure a smooth and efficient user experience during the entire purchase process.

## Transactions v. Distributed Tracing

What is the difference between transactions and [Distributed Tracing](../../../monitoring-and-insights/distributed-tracing/distributed-tracing/index.md)?

The primary distinction between transactions and Distributed Tracing features is the analysis context and depth. Transactions examine the entire sequence of events over time within a service. This method thoroughly explains how different parts of the transaction interact and evolve, providing deep insights into the internal dynamics of service requests.

While Distributed Tracing is adept at capturing snapshots of individual operations at specific moments, it does not effectively identify long-term bottlenecks within the overall flow. Transactions address this by delivering a detailed analysis that helps pinpoint and comprehend the underlying causes of performance issues across the entire flow of the service.

In essence, the transactions feature enhances the capabilities of Distributed Tracing by examining the collective impact of operations within a service, thus offering a more comprehensive view of their influence on the transaction's performance over time.

## Enablement

To enable transactions in your services for the supported programming languages, follow the guides listed below:

- [Node.js](../../../../opentelemetry/instrumentation-options/nodejs-opentelemetry-instrumentation/index.md#service-flows)

- [Java](../../../../opentelemetry/instrumentation-options/java-opentelemetry-instrumentation/index.md#service-flows)

- [Golang](../../../../opentelemetry/instrumentation-options/golang-opentelemetry-instrumentation/index.md#service-flows)

Once the Transactions plugin is added, the service begins generating spans that include the `cgx_transactions` attribute, which links each span (or segment) to its root transaction. When this attribute is detected in the metrics queried by APM, the Transactions tab is automatically displayed in the UI. This behavior is supported for all compatible programming languages.



## Monitoring transactions

The **Transactions** screen lets you identify service flows that may be good candidates for fine-tuning performance problems or resolving errors. It presents a high-level overview of the selected app's transactions, presented as operations.

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            To view information about your app's transactions, navigate to **APM** > **Transactions** from your Coralogix toolbar.
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            Select the Transaction Type and filter to view the transactions of interest.
            
            - Transaction types include:
                - **Web Flow**: Responds to external requests
                - **Pub-Sub Flow**: No expected responses (e.g., message queues)
                - **Internal Flow**: Self-triggered transactions (e.g., shutdown operations)
            - Once you have defined your view, the transactions' response time, throughput, and error rate will be displayed visually.
            - A grid will display all of the transactions matching your filter specifications. For each, you will be presented with average response time, P95 response time, average throughput (RPM), and error rate (per minute).
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            Clicking on a transaction for further investigation will transfer you to the **Segments** screen.

## Monitoring segments

The **Segment** screen summarizes the performance of the succeeding operations of the transaction root operation, the entry point for your transaction.

### Visualize segment performance

View the number of transaction requests, errors, and maximum response times for your transaction segments. Performance is presented in bar and line charts for the time frame chosen.

### Contextualizing segments

Each row of the **Segment Summary** presents a segment, a collection of related operations, with its performance over time.

Hover over a segment to view the exact placement of its constituent spans within a trace in Gantt view. This allows you to pinpoint the segment with the greatest impact on the transaction behavior over a specific timeframe. As spans do not appear as part of a trace linearly, you can move between spans to investigate each span and its connections within the transaction further.

Hover over a segment and click on the diagonal arrow to access an action menu for each segment: drill down, obtain a trace link, export to JSON, view the raw span, or see the entire trace map.

### Investigate spans and traces

Click on the **Segment** drill-down to investigate a segment further.

Under the **Segment Spans** tab, you will be presented with a full list of spans that comprise the particular segment, each with contextual data.

To focus on particular spans, click on a point in time on the graphs at the top of the page, presenting average span response times, requests, and errors within a segment.

Under the **Gantt** and **Map** tabs, select to view a trace from the dropdown menu. Sort traces using the following categories: most recent, max errors, max duration, above P95 and P99.

## Additional resources
|               |                                                                                                          |
|---------------|----------------------------------------------------------------------------------------------------------|
| Documentation | [Introduction to Application Performance Monitoring](../../getting-started/introduction-to-apm/index.md) |

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Contact us **via our in-app chat** or by emailing [<EMAIL>](mailto:<EMAIL>).
