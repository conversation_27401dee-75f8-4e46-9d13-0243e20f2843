---
title: "Introduction"
date: "2024-04-24"
description: "Dependencies monitors and analyzes how your instrumented services interact with databases, external APIs, third-party libraries, and other microservices."
---

# Dependencies

## Overview

APM Dependencies monitors and analyzes how your instrumented services interact with databases, external APIs, third-party libraries, and other microservices. By mapping these relationships, you gain actionable insights into how each dependency affects your application's performance.

![](../introduction/images/dependencies.png)

Use it to:

- **Find the root cause of slow database calls**. Identify which specific transactions (e.g., `/checkout`, `/search`) are generating the most expensive or overloaded queries.

- **Prioritize performance improvements**. Instead of optimizing in the dark, focus on the transactions that drive the majority of external call duration or errors.

- **Diagnose noisy neighbors**. Detect services or routes that are overwhelming shared dependencies like Kafka topics or external APIs.

### Detect external services and databases automatically

Automatically detect and track external services and databases that your application interacts with. For example:

- **HTTP/S API calls** (e.g., REST calls to external or internal services).
- **gRPC calls** to internal microservices or external APIs.
- **Database calls**: Queries to databases (e.g., MySQL, PostgreSQL, MongoDB).
- **Object storage interactions** (e.g., S3, GCS, Azure Blob).
- **Messaging systems** (e.g., Kafka, RabbitMQ, SQS, Pub/Sub).

These interactions are captured automatically as part of its instrumentation without requiring you to write additional code.

### Track performance metrics

Coralogix captures several critical performance metrics, such as response time, query time, error rate for each external call or database query.

![](../introduction/images/dependencies.png)

Metrics are presented in a unified view, enabling you to visualize metrics and trends related to your external service or database calls, identify performance bottlenecks, troubleshoot issues, and optimize service communication.

### Optimize external service or database performance

Monitor external calls or database queries to identify potential performance bottlenecks. For example, if one external service is consistently slow or experiences high error rates, you can take steps to optimize how your application interacts with that service, such as:

- Caching responses to reduce load on external services.
- Adjusting retry logic for failed requests.
- Switching to alternative services if needed.

### Explore external calls and database queries through root transactions

Users with the [Transactions plugin](../../transactions/index.md) can drill down into individual external calls or database queries to view the full distribution of transactions responsible for triggering the specific DB operation or external service call. This helps pinpoint the exact transactions that trigger specific queries and calls, enabling over-time analysis to identify which transactions most frequently execute a given operation, and are therefore most likely to be impacted by recurring errors correlated with that query or call.

![](../introduction/images/dependencies-transactions.png)

You can then filter external calls or DB queries to display only those triggered by the focused root transaction. Moreover, users can easily navigate to the root transaction details, including performance metrics and a breakdown of all associated segments. These insights provide deeper visibility into how a transaction is executed. This level of granularity ensures effective troubleshooting and performance optimization.

### Optimize external service or database performance

Monitor external calls or database queries to identify potential performance bottlenecks. For example, if one external service is consistently slow or experiences high error rates, you can take steps to optimize how your application interacts with that service, such as:

- Caching responses to reduce load on external services.
- Adjusting retry logic for failed requests.
- Switching to alternative services if needed.

### View Distributed Tracing

Open the full trace that a span belongs to to inspect specific database queries or external calls. Then, analyze detailed span attributes and metadata and generate shareable trace links for effective, collaborative troubleshooting.

## Enable APM Dependencies

## Prerequisites
To enable Dependencies, use the OpenTelemetry Span Metrics connector with Coralogix [Span Metrics](../../../getting-started/span-metrics/index.md). This allows you to visualize service dependencies and external calls within your application.

### Automatic setup

Dependencies are enabled by default when using the latest version of the [Coralogix Helm chart](https://github.com/coralogix/telemetry-shippers/blob/master/otel-integration/k8s-helm/Chart.yaml).

No further configuration is required.

### Manual setup

If you're not using the Helm chart, you can enable Dependencies manually by modifying the `spanMetrics` connector in your OpenTelemetry Collector configuration. Add the following `extraDimensions`:

```js
extraDimensions:
  - db.system
  - db.namespace
```
These dimensions are added as labels to the span metrics, allowing Coralogix to identify databases interactions.

For full analysis capabilities, add `db.collection.name` and `db.operation.name`.

### Root transaction analysis

For deeper insights—such as identifying which transactions initiate specific external calls—enable the [Transactions Plugin](../../transactions/index.md) in your application (for supported languages).

## Visualize Dependencies

- [View database queries](../view-database-queries/index.md)
- [View external calls](../view-external-calls/index.md)
