---
title: "View Database Queries"
date: "2024-04-24"
description: "View database queries to analyze database query patterns, uncover slow or failing dependencies, and trace them back to the root transactions that initiated them."
---

# View Database Queries

View database queries to analyze database query patterns, uncover slow or failing dependencies, and trace them back to the root transactions that initiated them. By combining visualizations, metrics, and end-to-end service context, you’ll gain the insight needed to troubleshoot issues faster, minimize latency, and improve the overall performance and reliability of your applications.

## Access database queries

After [enabling APM Dependencies](../introduction/index.md#enable-apm-dependencies), navigate to **APM > Service Catalog**. Click on a service of interest.  From within the Dependencies screen, select the **Databases** tab.

## Visualize database query trends

In the chart section, explore performance patterns by investigating key metrics related to database queries over time. Analyze trends in the most time-consuming database operations, query volumes, query durations, and failure rates. This will help you identify patterns, bottlenecks, and areas for optimization in your database interactions.

- **Time consuming**: The total time all instances of each DB call consumed during the selected time window, as a percentage of total clock time. Values may exceed 100% if multiple instances of the same call run in parallel.
- **Query time**: Query response time. Filter by average or P50, P75, P90, P95, P99 percentile.
- **Queries**: The total number of database queries.
- **Failures**: Query failures over time. Select to view the total number of query failures, failure percentage, or failures per second.

## Examine aggregated database call data

Use the database query table to explore and analyze all queries made by a service, consolidated across the databases it interacts with during the selected time frame. Delve into database operations along with their associated metadata, such as DB type, name, and table, to gain in-depth visibility into how your service interacts with databases and identify opportunities to optimize performance.

![](../view-database-queries/images/database-queries.png)

## Investigate a database transaction

Take a closer look at a specific database call to understand how it's being used and what’s triggering it.

- In the database grid, click on the DB call you want to investigate.
- You'll see a list of queries of the same type, executed during the selected time range.
- If the [Transactions Plugin](../../transactions/index.md) is enabled, each query includes its associated **root transaction**—the service transaction that initiated the call.
- Review query metrics, including charts showing success and failure counts, as well as query durations over time.

### Drill down with the Transactions Plugin

If the [Transactions Plugin](../../transactions/index.md) is enabled, you can explore even deeper:

- Analyze the Database Transactions pie chart. This shows the percentage of total query time attributed to each root transaction, calculated as:
    
    **(Total duration of DB calls per transaction) ÷ (Total DB call duration across all transactions)**
    
    Use this to identify which service transactions are driving the most load on the database operation.
    
- Click any segment in the pie chart to:
    1. **View queries from this transaction** — filter the table to show only queries triggered by the selected root transaction.
    2. **View transaction details** — open the [Transactions UI](../../transactions/index.md) to see the full breakdown of that root transaction, including its spans and performance metrics.
- Click on any query row to open the related [trace](../../../../monitoring-and-insights/distributed-tracing/distributed-tracing/index.md), giving you full context into how the database call fits into the broader transaction.

![](../view-database-queries/images/view-databases-transactions.png)

## Use Case: View database queries to understand the impact of a DB change

**"What happens if we change this table?"**

Before modifying a database—like renaming a column, restructuring a table, or changing a query—it’s critical to know what services and workflows depend on it. With Dependencies, especially when paired with the Transactions Plugin, you can trace exactly how a table or query is used across your system.

**1. Identify who’s using the table**

Start by searching for the table in the **Database Catalog**. You’ll see:

- Which services are interacting with it
- The types of operations (e.g., `SELECT`, `INSERT`, `UPDATE`) being executed
- Which service is making the most frequent calls

**2. Trace the query in context**

Next, navigate to the **Dependencies** tab for the relevant service and select the database operation tied to that table.

Here, you can:

- View a breakdown of **root transactions** (e.g., `/checkout`, `/search`) that trigger the query
- See how much **total query time** each transaction contributes
- Understand which flows are most at risk if the table or query is changed

**3. Follow the full path**

Drill down further:

- Use the pie chart to visualize which transactions account for the most query time
- Filter to view only queries triggered by a specific transaction
- Open the full trace to inspect span-level details and downstream impacts

This workflow gives you end-to-end clarity on how a database change might affect your application, before you make it.