---
title: "View External Calls"
date: "2024-04-24"
description: "Use external calls to analyze patterns, identify slow or failing dependencies, and trace them back to the root transactions that triggered them."
---

# View External Calls

Use external calls to analyze patterns, identify slow or failing dependencies, and trace them back to the root transactions that triggered them.

An external call refers to any outbound request your service makes to components outside its own codebase—such as APIs, microservices, cloud services, or third-party platforms.

With visualizations, metrics, and full-service context, you can quickly troubleshoot issues, reduce latency, and improve the overall performance and reliability of your applications.

## Access external calls

After [enabling APM Dependencies](../introduction/index.md#enable-apm-dependencies), navigate to **APM > Service Catalog**. Click on a service of interest.  From within the Dependencies screen, select the **External Services** tab.

## Visualize external call trends

In the chart section, explore performance patterns by investigating key metrics related to external calls over time. Analyze trends in call times, number of calls, response times, and error rates. This will help you identify patterns, bottlenecks, and areas for optimization in your interactions with external services.

- **Time consuming**: The total time spent on all instances of each external call during the selected time range, expressed as a percentage of total clock time. Values can exceed 100% when multiple instances run in parallel.
- **Response time**: The time it takes for the external service to process the request and respond to it. Filter by average or P50, P75, P90, P95, P99 percentile.
- **Requests**: The total number of external service calls.
- **Error rate**: The percentage of errors encountered when interacting with external services. Select to view the total number of errors, error percentage or errors per second.

## Examine aggregated external call data

Explore external call aggregations using the call grid, which consolidates detailed external call interaction data for the selected service and time frame. Delve into various call operations, including call methods, error rates, and other relevant parameters, to gain a comprehensive understanding of service interactions and optimize call performance.

![](../view-external-calls/images/dependencies-external-services.png)

## Investigate an external call

Dive into the details of a specific external call to understand how it's being used and what’s triggering it.

- In the external call grid, click on the call you want to investigate.
- You’ll see a list of calls of the same type, executed within the selected time range.
- If the [Transactions Plugin](../../transactions/index.md) is enabled, each call also includes its associated **root transaction**—the service transaction that initiated the external request.
- Review external call metrics, including charts showing success and failure counts, as well as response times.

### Drill down with the Transactions Plugin

If the Transactions Plugin is enabled, you can explore even deeper:

- Analyze the **call rate** pie chart. It shows the share of total external call duration attributed to each root transaction, calculated as:
    
    **(Total duration of external calls per transaction) ÷ (Total duration of all external calls across all transactions)**
    
    Use this to identify which service transactions are most frequently interacting with the external service.
    
- Click any section in the pie chart to:
    1. **View calls from this transaction** — filter the table to show only external calls triggered by the selected root transaction.
    2. **View transaction details** — open the [Transactions UI](../../transactions/index.md) to explore the root transaction and its related segments.
- Click on any span row to open the [full trace](../../../../monitoring-and-insights/distributed-tracing/distributed-tracing/index.md) that includes the external call, giving you complete context within the transaction flow.

![](../view-external-calls/images/external-services-transactions.png)

![](../view-external-calls/images/external-calls-by-transaction.png)

## Use case: Investigating a service issue caused by an external dependency

**Part 1: Detecting the issue**

A Coralogix service alert flags a performance degradation—perhaps increased latency or a spike in errors. The engineering team begins troubleshooting, but the root cause isn’t clear yet.

The key questions:

- *Is this caused by an external service?*
- *If so, which one, and what’s the impact?*

**Part 2: Identifying the problematic external call**

The team navigates to the **Dependencies** tab for the affected service and opens the **External Services** view.

From there, they can immediately spot:

- Which external APIs or services are consuming the most time
- Which calls are generating the most errors
- How these metrics trend over time

They quickly identify the culprit—e.g., a slow or failing call like `POST /v1/payments`—and drill into its details.

**Part 3: Tracing the impact across transactions**

Inside the external call details, they analyze:

- Which root transactions are triggering the external call most frequently
- Which transactions are most affected, based on the total time spent on the call

This helps answer:

- *Which user flows or internal operations are impacted?*
- *Is one transaction overusing the API and causing performance issues?*

**Optional deep dive: Full transaction trace**

With the [Transactions Plugin](../../transactions/index.md) enabled, they go deeper by:

- Viewing the segment breakdown of the root transaction, from the entry point to the external call
- Identifying whether the latency originates before, during, or after the external interaction
- Opening the full trace for complete execution context

This workflow enables teams to move quickly from **alert → root cause → impacted flows**, reducing time-to-resolution and improving system resilience.