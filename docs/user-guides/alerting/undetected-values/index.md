---
title: "Manage Undetected Values"
date: "2023-12-31"
description: "If a monitored value stops being sent, alerts may repeatedly trigger for each evaluation timeframe. You can manage this behavior by configuring undetected values settings."
---

# Manage Undetected Values

## Overview

Threshold alerts based on [logs](../create-an-alert/logs/threshold-alerts/index.md) or [metrics](../create-an-alert/metrics/threshold-alerts/index.md) may encounter **undetected values**, especially when using the **less-than** condition. If a monitored value stops being sent, alerts may repeatedly trigger for each evaluation timeframe. You can manage this behavior by configuring **undetected values settings**.

## Configure undetected values settings

### Enable or disable triggering

During alert setup, you can enable or disable triggering based on undetected values. If enabled, alerts trigger when values are undetected. If disabled, undetected values do not trigger alerts.

![](../undetected-values/images/manage-undetected-values.png)

### Configure auto-retirement

Undetected values can trigger alerts continuously at set intervals. Auto-retirement allows you to specify a time window after which the alert stops triggering for the same undetected value. By default, auto-retirement is set to **never**, meaning alerts continue indefinitely until manually addressed.

Example: If a log query monitors status **200**, and a service change results in undetected values, the alert will keep triggering unless auto-retirement is configured.

## Retire undetected values manually

When Coralogix detects undetected values, you can manually retire them in the alert definition.

!!! note

    Values displayed in the alert definition reflect current undetected values, which may differ from those that triggered the alert.

Select any value from the list to retire it manually and stop the alert from triggering for that value.

![](../undetected-values/images/retire-undetected-values.png)

## View undetected values in Incidents

Triggered alerts appear in [Incidents](https://coralogix.com/docs/user-guides/alerting/incidents/), showing detected and undetected values.

When an alert triggers **only undetected values**, they are displayed in a grid, and the line chart is empty.

![](../undetected-values/images/undetected-values-incidents.png)

When an alert triggers **both detected and undetected values**, detected and undetected values appear in the line chart, while a list of undetected values are shown in a grid.

!!! note

    Displayed undetected values are historical and may not reflect the current state.

## Limitations

Alerts for undetected values trigger only if the value is missing in **two consecutive 1-minute evaluation cycles**. This introduces a **1-minute delay** but helps reduce false alarms caused by temporary gaps.