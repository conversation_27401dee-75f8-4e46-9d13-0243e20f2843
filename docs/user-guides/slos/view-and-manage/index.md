---
title: "View and Manage SLOs"
date: "2024-03-14"
description: "Stay on top of system reliability with clear visuals, real-time status, and actionable insights in SLO Hub."
noindex: true
search:
  exclude: true
---
# View and Manage Service Level Objectives in SLO Hub

## Overview

**SLO Hub** is the centralized dashboard for tracking and managing Service Level Objectives across your environment. It helps you stay on top of system reliability with clear visuals, real-time status, and actionable insights.

Use it to:

- Create and update SLO definitions and associated alerts all in one place
- Monitor SLO compliance, remaining error budgets, and performance trends at a glance
- Filter and search SLOs by labels, status, and remaining budget
- Drill down into individual SLOs to analyze grouped data, alerts, and triggered alert events

## Visualize your SLO performance trends

SLO Hub provides a high-level view of your system's health. Use the visualizations to explore your overall system compliance based on the statuses of all SLOs tracked over time.

Spot dips that may signal SLO pressure or upcoming risk. The interface categorizes each SLO or SLO group based on current status:

| **Status** | Remaining error budget |
| --- | --- |
| **OK** | >75% to 100% |
| **Warning** | >25% to ≤75% |
| **Critical** | >0% to ≤25% |
| **Breached** | 0% |

## Examine aggregated SLO data

Scroll down to view the **SLO grid**, which lists all SLOs in your account. 

Each column includes key indicators that help you evaluate performance at a glance.

| **Parameter** | **Description** |
| --- | --- |
| **Name** | The user-defined name of the SLO. |
| **Grouping key** | The key (e.g., `service.name`, `customer.id`) used to break the SLO into multiple groups for individual tracking. |
| **SLO status** | The current health status (OK, Warning, Critical, Breached).  For grouped SLOs, status across all groups is displayed. |
| **Lowest remaining error budget** | % of allowable errors left before breaching the SLO. For grouped SLOs, the information for the group with the lowest remaining budget is displayed.  |
| **Target** | The success percentage target (e.g., 99.9%). |
| **Time frame** | The compliance period (e.g., 7 days, 28 days). |
| **Alerts** | Number of alert events triggered for the SLO (if alerts are set). |
| **Creator** | The user who created the SLO. |
| **Entity labels** | A custom label assigned to the SLO at creation, used for filtering or grouping (e.g., `env:prod`, `team:payments`). |
| **Alerts** | Number of unique SLO alerts identified during the SLO time frame |
| **Last updated** | Timestamp of the most recent change to the SLO's configuration. |

**Tip:** Click any column header to sort the table—for example, sort by lowest remaining budget to quickly find the most at-risk SLOs.

## SLOs actions

You can take action on any SLO directly from the Hub.

1. Hover over the SLO row.
2. Click the **ellipsis (⋯)** icon on the left.
3. Choose an action:

| **Action** | **Description** |
| --- | --- |
| **Edit** | Update thresholds, queries, labels, and other configuration parameters. If any changes invalidate existing alerts, you’ll be prompted to modify or remove those alerts before saving. Historical data for the previous SLO configuration will no longer be retained. |
| **Create an alert** | Define alerting conditions based on the SLO’s logic to detect potential or actual violations. Alerts help teams respond proactively to reliability risks. |
| **Delete** | Remove obsolete SLOs to keep your view focused and accurate.  When an SLO configuration is deleted, its historical data will no longer be available and its associated alerts are deleted. |

## Drill down into a specific SLO

Click any SLO row to open the **SLO Details** view, where you can:

- View SLO configuration
- View current statistics
- Visualize performance metrics over time
- Monitor specific permutations (for grouped SLOs)

### View SLO definitions

| **Parameter** | **Description** |
| --- | --- |
| **SLO name** | The name assigned to the SLO by the user. |
| **Selected permutation** | Each SLO is evaluated per permutation. If the SLO is grouped, the displayed charts and statistics reflect only the currently selected permutation. By default, the permutation with the **lowest remaining error budget** is selected, helping prioritize the most at-risk objective. |
| **SLO target** | The defined performance goal for the SLO, e.g., 99% over the past 7 days. |
| **SLO query** | The PromQL expression(s) used to compute the service level indicator (SLI). |
| **SLO alerts** | Hovering over this element reveals the alert names associated with the SLO. Clicking opens the SLO Alert Configurator, allowing you to view or edit the alert configuration for the selected alert. |

Let me know if you need a markdown-friendly or Notion-compatible version!

### View SLO statistics

In the Details view, you’ll see key metrics for the selected SLO. If the SLO includes grouping (e.g., `by instance`), use the dropdown to filter by specific permutations.

Metrics include:

- **Current compliance** – The latest SLI value.
- **Error budget remaining** – Shown as a progress bar (e.g., 82%).

### Visualize SLO performance metrics

Performance charts show how your SLO is trending over time. These visualizations help you understand whether you're within budget, approaching a threshold, or burning error budget too quickly.

| **Chart** | **Description** |
| --- | --- |
| **Remaining error budget** | Tracks the available error budget over time, calculating the remaining budget at each point based on the selected time frame. For example, in a 14-day SLO, each point represents the remaining error budget over the past 14 days (from the selected point). |
| **Compliance over time** | SLI performance measured in fixed buckets. Helps spot patterns without relying on a rolling average. |
| **Burn rate** | A multiplier that shows how fast the error budget is being consumed in fixed buckets. High spikes indicate instability. |
| **Good vs bad events** *(event-based SLOs only)* | Counts of successful vs. bad events per time step. Useful for pinpointing failure surges. |

### Monitor specific permutations

For grouped SLOs, all permutations are displayed.  Triggered alert events per permutation appear in the **Alerts** column. Click the bell icon to select from the alerts list and view the latest triggered alert event within the **Watch Data** UI.