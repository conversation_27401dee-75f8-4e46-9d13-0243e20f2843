---
title: "SLO Alerts"
date: "2024-03-14"
description: "Get alerted when you are at risk of breaking SLOs with SLO alerts."
noindex: true
search:
  exclude: true
---

# SLO Alerts

Service Level Objectives help teams define what reliability means for their services. But knowing whether you're meeting your SLOs isn’t enough, you need to be alerted when you're at risk of breaching them. That’s where SLO-based error budget and burn rate alerts come in.

## Overview

SLO alerts provide actionable, reliability-centered monitoring, shifting focus away from individual incidents to the user experience as a whole. Instead of reacting to every spike or dip, you can prioritize based on real impact to service goals.

At present, SLO alerts support [event-based SLOs](../create/index.md).

### Track error budget usage

Your error budget represents how much unreliability you can tolerate before breaching your SLO. Monitoring it gives you a clear picture of how much room you have left to absorb incidents. This enables you to make smarter decisions about whether to ship a risky release, investigate performance regressions, or prioritize reliability improvements over new features.

### Detect and react to burn rate spikes

Burn rate shows how quickly you're using up your error budget. By tracking this rate, you can differentiate between high-impact, fast-burning incidents that require immediate attention and slow-burning degradations that may need longer-term investigation. This empowers you to respond with the appropriate urgency based on the actual risk to your SLO.

### Reduce alert noise and fatigue

Traditional alerting systems often overwhelm with alerts that may not reflect real user impact. SLO-based alerting focuses on the experience that actually matters to users, drastically cutting down false positives and alert fatigue. You can prioritize fewer, higher-quality alerts that signal true reliability threats.

## Getting started

Create SLO alerts using one of the following methods:

- In the Coralogix UI, go to **Alerts** > **Alert Management**. Click **NEW ALERT**, and select **SLO** as your alert type.
- When saving a newly-created SLO, select **Save & create alert**.
- In the SLO Hub, hover over an SLO in the SLO grid, click on the ellipsis (…) at the beginning of the row, and choose **Create alert**.

## Select SLO and define alert details

1. Use the SLO drop-down box to select an existing SLO or create a new one. If needed, click **View SLO** to navigate to the SLO details page in the SLO Hub.
2. Define the [alert details](../../alerting/define-alert-details/index.md).

## Error budget alert

Error budget alerts are triggered when the remaining error budget percentage is equal to or below a defined threshold.

These alerts ensure you are notified early when your service is at risk of falling below its threshold, enabling you to take proactive action before breaching your reliability goals. 

### Add a budget threshold

Set when the alert should trigger based on error budget usage, and assign its priority (P1 to P5). For example, trigger a P1 alert at 10% consumption for urgent action, or a P3 alert at 50% for early warning. 

- You can define up to five unique thresholds for each alert.
- Avoid using the same percentage value or assigning the same priority level more than once within a single alert configuration.

## Burn rate alert

Burn rate alerts are triggered when the SLO error budget is consumed faster than the expected burn rate.

There are two types of burn rate alerting strategies: **single-window** and **dual-window**. Each serves a different operational need for balancing fast incident detection and alert stability.

### Single window

A single-window burn rate alert monitors the error budget consumption over one rolling time window (e.g., over the past 1 or 48 hours). 

Single-window alerts are ideal for detecting sustained issues over a fixed period.  They allow for rapid detection of critical failures, when immediate responsiveness outweighs occasional noise.

### Dual window

A dual-window burn rate alert uses two time windows, short and long, to balance speed and stability in alerting.  

The alert only triggers if the threshold for **both** the short and long windows is exceeded.

Dual-window alerts are ideal for production-critical services where alert noise must be minimized. Or, for environments requiring early yet reliable detection of ongoing incidents.

### Example: How dual-window burn rate alerting works

Let’s assume a long window of 1 hour, a short window of 5 minutes, and a burn rate threshold of X14.

![](../alerts/images/short-long-windows.png)

When the burn rate alert triggers at minute 5, it doesn’t mean we’re only evaluating that single minute. Instead, at minute 5, the system evaluates the previous 5 minutes (short window) and the previous 1 hour (long window) to determine if the burn rate exceeds the defined threshold.

An alert is triggered only if both the short and long windows exceed the threshold at the same evaluation point (minute 5).

The long window helps prevent false positives caused by short-lived spikes.

In the example above, although the error stops at minute 10, the long window still includes the earlier error data. If only the long window was used, the alert would remain active for longer. That’s where the short window adds value—it allows for quicker alert resolution. Using both ensures timely detection without remaining in an alert state longer than necessary.

### Define the evaluation window

Select a time window for burn rate evaluation:

- **Single window** – Evaluates the burn rate over a single, fixed time frame (e.g., the past 1 hour). This is useful for straightforward, time-bound alerting.
- **Dual window** – Evaluates the burn rate across two time frames: a short-term window and a long-term window (e.g., past 5 minutes and past 1 hour).
    
    This approach allows for both fast-response alerts and confirmation over a longer period to reduce noise.
    
!!! note

    In dual window mode, the short window is automatically calculated as 1/12 of the long window, following best practices outlined in the Google SRE Workbook.

### Define burn rate thresholds

Define how many times faster than normal the error budget is burning and assign it a priority (P1 to P5). For example, send a P1 alert if the burn rate is> 1.5 normal usage.

!!! note

    Avoid using the same priority level more than once within a single alert configuration.

## Notifications

Define the alert [notification settings](../../alerting/configure-notifications/settings/index.md).

## Schedule

Define an alert schedule by specifying the exact days and time ranges during which the alert should be active.

## View triggered alert events in Incidents

Triggered SLO-based alert events are aggregated into [Incidents](../../alerting/incidents/index.md), providing a centralized way to track, investigate, and resolve critical issues across your system. Each incident aggregates related alerts, giving you a high-level view of what went wrong, when, and where. From within the Incidents view, you can dive into the relevant SLO permutation to understand the root cause faster.

In this example, the pink represents the P1 threshold, while the green line shows the actual burn rate.