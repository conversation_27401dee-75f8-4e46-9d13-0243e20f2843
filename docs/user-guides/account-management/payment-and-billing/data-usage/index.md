---
title: "Data Usage"
date: "2021-03-03"
coverImage: "data-usage.png"
description: "Understand how data usage is calculated, monitored, and reported in Coralogix."
---

Coralogix provides transparent, flexible, and detailed insights into your observability Data Usage. Whether you're managing logs, metrics, traces, or AI-driven evaluations, usage is measured in units—a consistent billing metric designed to give you control over your data volume, cost, and performance.

This guide explains how Data Usage is calculated, monitored, and reported in Coralogix. You'll learn how to understand your billing model, interpret usage statistics, and make the most of the Data Usage interface and reports to optimize your account.

![](../data-usage/images/data-usage-metrics-tab;e.png)

## Understand Coralogix Data Usage

### Unit-based pricing model

Coralogix uses a [unit-based pricing model](https://coralogix.com/pricing/) across most observability features. A **unit** serves as a universal billing metric for all data types.

Ingested data flows through one of several [TCO pipelines](../../tco-optimizer/logs/index.md), where each pipeline’s priority determines its unit cost. If no pipeline is explicitly assigned, the data is treated as high priority (Frequent Search).

Usage is measured against your [daily unit quota](../plan-and-payments-management/index.md).

### Logs

| Pipeline | Sent data | Units |
| --- | --- | --- |
| High priority (Frequent Search) | 1 GB | 0.75 |
| Medium priority (Monitoring) | 1 GB | 0.32 |
| Low priority (Compliance) | 1 GB | 0.12 |
| Blocked logs | 1 GB | 0.065 |

> Blocked data is charged at 8% of its original size to account for network ingestion overhead.

### Metrics

| Pipeline | Sent data | Units |
| --- | --- | --- |
| Metrics | 30 GB | 1 |

### Traces

| Pipeline | Sent data | Units |
| --- | --- | --- |
| High priority (Frequent Search) | 1 GB | 0.5 |
| Medium priority (Monitoring) | 1 GB | 0.25 |
| Low priority (Compliance) | 1 GB | 0.1 |

> 1 unit = $1.50 of usage for logs, metrics, or traces in any pipeline.

### Pricing example

- **1.3 GB of logs** in the **Frequent Search** pipeline (at ~$1.15/GB) = **1 unit**
- **3 GB of logs** in the **Monitoring** pipeline (at ~$0.50/GB) = **1 unit**

### AI evaluators

AI evaluators help assess LLM performance as part of Coralogix’s [AI observability](../../../ai-observability/ai-center/index.md) suite. Their pricing is based on token volume and processing complexity.

1. **Tokens**: All input text is measured in tokens (small chunks of text, such as words or symbols).
2. **Enabled evaluators**: Each evaluator performs a layer of analysis on the tokens. Multiple evaluators may be used simultaneously.
3. **Usage formula**:
    
    `Units = (tokens × enabled evaluators) / 1,000,000`
    

### AI pricing example

If an AI agent processes 10,000 tokens using 5 evaluators:

`(10,000 × 5) / 1,000,000 = 0.05 units`

## Monitor usage in the UI

The Data Usage UI provides real-time visibility into how units are consumed across your Coralogix account. 

Navigate to **Settings > Data Usage** to access the interface.

### Time picker

Select a time range for viewing usage:

- Current month (default)
- Last 30 days
- Last 90 days

### Usage tabs

The Data Usage UI includes three main tabs:

- **Total quota units**: Daily unit consumption
- **Data sent**: Total ingested data volume
- **AI evaluators**: Units consumed by LLM evaluations

### Usage statistics

![](../data-usage/images/data-usage-metrics-tab;e.png)

Each tab presents three key metrics:

- **Total**: Total billing units consumed during the selected date range
- **Daily max**: Peak unit usage on any single day
- **Max saved quota**: Units saved via TCO optimization

### Units per entity

Hover over columns in the bar chart to view unit distribution by day, or scroll to the breakdown grid for detailed totals by entity type.

![](../data-usage/images/data-usage-units-per-entity.png)

### Units breakdown

For each entity, users are presented with the following breakdown for the time range selected in the time picker:

- The total number of units attributed to the entity and the percentage share of the total unit consumption.
- The volume of ingested data attributed to that entity.

## Enhance observability with data usage metrics

Enable data usage metrics to gain deeper, real-time insight into usage patterns. This feature lets you build dashboards, set alerts, and generate detailed summaries around data usage. [Learn more](https://coralogix.com/docs/user-guides/account-management/payment-and-billing/data-usage-metrics/).

## Generate and export usage reports

### Standard data usage report

Generate a standard data usage report to view unit consumption and ingested data for the past 90 days.

### Detailed data usage report

Generate a detailed data usage report to view all ingested data by pipeline for the current month, the past 30 days, or the past 90 days. This report provides a policy-level, day-by-day view of ingested data per entity and priority, and units consumed. Reports can be accessed via the UI or through the [Data Usage Service API](https://coralogix.com/docs/developer-portal/apis/data-management/data-usage-service-api/).

CSV exports include:

- Date
- Application
- Subsystem
- Severity
- TCO priority
- Entity type
- Ingested data (GB sent)
- Unit consumption

## Permissions

Depending on which actions you would like to execute with Data Usage, the following [permissions](http://127.0.0.1:8000/docs/user-guides/account-management/user-management/create-roles-and-permissions/) are necessary.

| Resource | Action | Description |
| --- | --- | --- |
| `data-usage` | `manage` | Manage Data Usage |
| `data-usage` | `read` | View Data Usage |