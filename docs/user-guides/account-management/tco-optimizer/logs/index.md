---
title: "TCO Optimizer: Logs"
date: "2020-08-03"
coverImage: "<EMAIL>"
---

By allowing you to define the data pipeline for your logs based on the importance of that data to your business, our **Total Cost of Ownership Optimizer** not only reduces your logging costs by up to two-thirds, but also improves your real-time analysis and alerting capabilities.

Find information on our **Tracing TCO Optimizer** [here](../traces/index.md).

## Overview

The TCO Optimizer allows you to assign different logging pipelines for each application and subsystem pair and log severity. In this way, it allows you to define the data pipeline for your logs based on the importance of that data to your business.

The feature enables you to get all of the benefits of an ML-powered logging solution at only a third of the cost, with an improved ability to query, monitor, and manage your data.

Find information on our Tracing TCO Optimizer [here](../traces/index.md).

## TCO data pipelines

### Compliance data (low-priority)

Log data that needs to be kept for compliance/post-processing reasons will be archived immediately.

Features available include:

- [Log Parsing](../../../data-transformation/parsing/log-parsing-rules/index.md)

- [LiveTail](../../../monitoring-and-insights/livetail/index.md)

- [Archive](../../../data-flow/s3-archive/connect-s3-archive/index.md)

- [Reindex](../../../data-query/log-query/log-query-simply-retrieve-your-data/index.md)

- [Background Queries](../../../data-query/background-queries/index.md)

- [Session Replay](../../../rum/product-features/session-replay/index.md)

### Monitoring data (medium-priority)

Logs that are used for monitoring or statistics will be fully available for those use cases. This allows you to define alerts, build dashboards, view statistics, query the live data stream, and receive proactive anomalies.

Features available include:

- [Dashboard Visualizations](../../../custom-dashboards/getting-started/index.md)

- [LiveTail](../../../monitoring-and-insights/livetail/index.md)

- [Loggregation](../../../monitoring-and-insights/loggregation-making-big-data-small/index.md)

- [Events2Metrics](../../../monitoring-and-insights/events2metrics/index.md)

- [Log Parsing](../../../data-transformation/parsing/log-parsing-rules/index.md)

- [Alerts](../../../alerting/create-an-alert/logs/threshold-alerts/index.md)

- [Anomalies](../../../alerting/incidents/index.md)

- [Archive](../../../data-flow/s3-archive/connect-s3-archive/index.md)

- [Reindex](../../../data-query/log-query/log-query-simply-retrieve-your-data/index.md)

- [Background Queries](../../../data-query/background-queries/index.md)

- [Real User Monitoring](../../../rum/getting-started/real-user-monitoring/index.md)

- [Custom Dashboards](../../../custom-dashboards/getting-started/index.md)

### Frequent Search data (high-priority)

Typically high-severity or business-critical data, logs in this pipeline need to be individually queried and analyzed. These logs are stored on highly available SSDs, replicated, and ready to be queried within seconds.

Features available include:

- [Lightning Queries](../../../data-query/log-query/log-query-simply-retrieve-your-data/index.md)

- [LiveTail](../../../monitoring-and-insights/livetail/index.md)

- [Dashboard Visualizations](../../../custom-dashboards/getting-started/index.md)

- [Loggregation](../../../monitoring-and-insights/loggregation-making-big-data-small/index.md)

- [Logs2Metric](../../../monitoring-and-insights/events2metrics/index.md)

- [Log Parsing](../../../data-transformation/parsing/log-parsing-rules/index.md)

- [Alerts](../../../alerting/create-an-alert/logs/threshold-alerts/index.md)

- [Anomalies](../../../alerting/incidents/index.md)

- [Archive](../../../data-flow/s3-archive/connect-s3-archive/index.md)

- [Reindex](../../../data-query/log-query/log-query-simply-retrieve-your-data/index.md)

- [Background Queries](../../../data-query/background-queries/index.md)

- [Real User Monitoring](../../../rum/getting-started/real-user-monitoring/index.md)

- [Custom Dashboards](../../../custom-dashboards/getting-started/index.md)

## TCO Optimizer screen

The TCO Optimizer screen includes a **Logs** tab, split into three sections:

- Distribution

- Policy Criteria

- Application and Policy Overrides

## Distribution

The **Distribution** section presents the percentage of logs for each TCO pipeline: High - Frequent Search, Medium - Monitoring, and Low - Compliance. The Compliance pipeline includes low and blocked logs.

![Coralogix TCO Optimizer](images/Screen-Shot-2021-06-26-at-5.09.58-PM.png)

## Policy citeria

The **Policy Criteria** section includes current policies and allows for the creation of new ones. Policies are applied to combinations of applications, subsystems, and severities as the logs are ingested and will assign them the appropriate TCO pipeline based on the policy content. In this way, they simplify assigning TCO pipelines and capture any applicable future logs on ingestion. Each policy creates new default values for the logs for which the policy is applicable.

!!! note

    - The default policy for all logs is high.

    - If policies conflict, the first policy appearing on the screen will take precedence.

  
![TCO Policy criteria](images/Screen-Shot-2021-11-21-at-18.35.29.png)  

### Create a new log policy

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            Click **\+ ADD NEW POLICY.**
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            Choose the relevant filters for applications, subsystems and severities.
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            Set the priority.
            
            These will become the default TCO pipelines assignments for all matching logs that will be ingested into Coralogix.
    ::: custom-list-item marker=4
        ::: list-item-header

        ::: list-item-content
            Once you finish selecting filters and setting the priority, click **Save**.

## Application and policy overrides

The **Application and Policy Overrides** section displays the usage of all applications and subsystems producing logs, sorted by the top consumers. You may use the top filters to easily locate a specific component. Clicking on any row will allow you to see a detailed view of the application-subsystem pair usage organized by level of severity and its default TCO pipeline assignments as created initially.

In this view, you are able to change the priority for an **entire** application-subsystem pair or to change the priority for **specific** severities within any application-subsystem pair. The priority will determine the data pipeline through which the data for this subsystem will be channelled. If there are different TCO pipelines for different severities, the policy status will be **Multiple**.  
![TCO Policy override](images/Screen-Shot-2021-11-21-at-18.39.23-e1637513686646.png)

![TCO Tune Severity](images/Screen-Shot-2021-11-21-at-19.26.28.png)

  
To begin overriding from the beginning, remove all overrides by clicking **Reset All Overrides**.

![TCO - Reset all overrides](images/Screen-Shot-2021-11-21-at-19.29.38.png)

## Additional resources
|               |                                                                                                                                                                                                                                    |
|---------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Documentation | [Tracing TCO Optimizer](../traces/index.md)<br/>[Archive Retention Policy](../../../data-flow/s3-archive/archive-retention-policy/index.md)                                                                                        |
| API           | [TCO Optimizer API](../../../../developer-portal/apis/tco-controls/tco-optimizer-http-api/index.md)<br/>[Archive Setup gRPC API](../../../../developer-portal/apis/tco-controls/archive-setup-grpc-api/index.md) |

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email at [<EMAIL>](mailto:<EMAIL>).
