---
title: "Ingestion & Query Limits"
date: "2024-02-04"
description: "This guide outlines the metering limits Coralogix applies to metrics usage per team."
---

# Ingestion & query limits

This guide outlines the metering limits Coralogix applies to metrics usage per team. 

## Metering units

To maintain a consistent and predictable user experience, Coralogix sets usage limits per team based on daily and hourly metering units.

| **Metering unit** | **Definition** | **Additional notes** |
| --- | --- | --- |
| **Day** | Begins at midnight (00:00) UTC and spans 24 hours | Some metrics from one day may carry over into the next day. |
| **Hour** | Begins at the start of each hour (e.g., 00:00, 01:00) and lasts exactly one hour, ending at 59 minutes and 59.999 seconds (e.g., 00:00 - 00:59:59.999) | Some metrics from one hour may carry over into the next hour. |

## Metric metadata limits

### Overview

| Limit name | Description | Limit (bytes) |
| --- | --- | --- |
| Label length | String length of a label value | 512 |
| Max labels | Maximum number of labels per metric | 100 |

These global limits are not adjustable.

### Actions following limit breaches

- **Label length**:  If a sample includes a label that exceeds the allowed length, Coralogix discards the sample altogether. These dropped samples do **not** count toward your data usage. The dropped samples are logged and will be made available for reporting.
- **Maximum labels per metric**: If a sample includes more labels than the allowed limit, Coralogix discards the samples containing that metric. These dropped metrics do **not** count toward your cardinality usage. The dropped samples are logged and will be made available for reporting.
- If a metric includes more labels than allowed, Coralogix discards the sample altogether. These dropped samples do **not** count toward your data usage. The dropped samples are logged and will be made available for reporting.

## Ingestion limits

### Overview

| **Meter name** | **Description** | **Default Limit** |
| --- | --- | --- |
| Unique series per metric | Unique times series (cardinality) ingested per metric name per day | 24,000,000  |

### Actions following limit breaches

**Unique series per metric**: This limit is enforced at per metric level for the team account. If the daily cardinality limit is reached, Coralogix blocks the ingestion of the metric until the limit resets the next UTC day.  

This limit can be increased by working with your account team if your use case requires high cardinality ingestion than allowed by the default limit.

## Query limits

| **Meter name** | **Description** | **Entity** | **Default Limit** |
| --- | --- | --- | --- |
| Total series analyzed | No. of time series (cardinality) analyzed in a query | Recording Rules, Alerts, Dashboards | 300k |
| Total samples analyzed | Total number of samples analyzed from the storage per query | Recording Rules, Alerts, Dashboards | 300k |

Limits are numerical thresholds tracked based on usage. Each limit has an associated meter that monitors whether it has been exceeded.