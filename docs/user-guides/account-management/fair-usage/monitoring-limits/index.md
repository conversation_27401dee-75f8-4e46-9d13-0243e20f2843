---
title: "Monitoring Fair Usage Limits"
date: "2024-02-04"
description: "Effectively monitor and manage fair usage limits to prevent ingestion delays, performance degradation, or service interruptions."
---

# Monitoring Fair Usage Limits

Effectively monitor and manage fair usage limits to prevent ingestion delays, performance degradation, or service interruptions.

## Prerequisites

After configuring your [S3 metrics bucket](../../../data-flow/s3-archive/connect-s3-archive/index.md), enable the data usage metrics feature. Once enabled, Coralogix will auto-populate data for the past 24 hours, a process that may take up to two hours.

## Accessing limits

Navigate to **Settings** > **Metric Data** > **Fair Usage Limits** in the Coralogix toolbar.

Under the **Fair Usage Limits** tab, you will find each metric with imposed limits, including its category, description, and thresholds. Limit breaches, if any, are displayed in the right-most column. 

![](../monitoring-limits/images/fair-usage-1.png)

## Viewing limit breaches over time

For each limit, the number of breaches within the selected timeframe will be displayed as a bar or line chart when one clicks on the drop-down arrow.

![](../monitoring-limits/images/fair-usage-2.png)

Hover over a point on the graph to see the exact number of violations and corresponding dates.

![](../monitoring-limits/images/fair-usage-3.png)

## Monitoring limits in Custom Dashboards

Metrics can also be monitored in [Custom Dashboards](https://coralogix.com/docs/user-guides/custom-dashboards/getting-started/).  For example, a user can create a line chart with `unique_series_daily` meter using the queries below:

```sql
max(unique_series_daily) by (metric_name)
```

Retrieves the maximum number of unique time series ingested per metric, helping to track peak cardinality usage.

```sql
increase(unique_series_daily[30m])
```

Calculates the increase in unique time series over the past 30 minutes, allowing users to identify ingestion trends and spikes.

## Monitoring limits with Alerts

Metrics may also be monitored with [Alerts](../../../alerting/introduction-to-alerts/index.md).  For example, a user might build a [metrics-based threshold alert](../../../alerting/create-an-alert/metrics/threshold-alerts/) that monitors `unique_series_daily`, which tracks cardinality growth and detects potential limit violations, using this example query:

```sql
max(unique_series_daily) by (metric_name)
```

When the number of unique time series exceeds a specific threshold, matching one or more [conditions](../../../alerting/multiple-alert-conditions/index.md), an alert is triggered, enabling proactive adjustments before limits impact system performance.

## Requesting limit adjustments

If your team consistently approaches or exceeds fair usage thresholds and requires additional capacity, Coralogix offers flexibility through managed adjustments.

To explore increasing your ingestion or query limits, contact Customer Support via our in-app chat or by email at [<EMAIL>](mailto:<EMAIL>).

Global limits are not adjustable.