---
title: Test
meta: [ { keywords: "example keyword 1, example keyword 2" }, { author: 'Coralogix' } ]
parent_sections: [ 'Integrations', 'Security' ]
tags:
  - Monitoring
  - Alerts
show_blocks:
  - global_category_selector
  - domain_selector
  - buttons_nav
noindex: true
search:
  exclude: true
---

Test features

$$
\text{SLI} = \frac{9800}{10000} = 0.98\ (\text{or } 98\%)
$$

#### Tabs
/// tab | Tab 1 title
Tab 1 content
///

/// tab | Tab 2 title
Tab 2 content
///

![Image title](https://dummyimage.com/2000x400/eee/aaa)

```dataprime
source logs | stats count by field1 | extract $d.field into $d.new_field using kv()
```

```dataprime
source logs | countby $d.Category | extract $d._count:string into $d.new_field using kv()

```

```dataprime
create $d.radius from 100+23
c $d.log_data.truncated_message from $d.message.substring(1,50)
c $d.trimmed_name from $d.username.trim()

create $d.temperature from 100*23
```

#### Domain
```
URL: "{{ endpoints.logs.bulk }}". Input your Coralogix [domain](../user-guides/account-management/account-settings/coralogix-domain/index.md).
```

| Region                                               | URL                                                             |
|------------------------------------------------------|-----------------------------------------------------------------|
| US1 <br /> US2 <br /> EU1 <br /> EU2 <br /> AP2 (SG) | https://api.eu2.coralogix.com/api/v1/external/integrations/     |
| AP1 (IN)                                             | https://api.app.eu2.coralogix.com/api/v1/external/integrations/ |

|                                                      |                                                                 |
|------------------------------------------------------|-----------------------------------------------------------------|
| US1 <br /> US2 <br /> EU1 <br /> EU2 <br /> AP2 (SG) | https://api.eu2.coralogix.com/api/v1/external/integrations/     |
| AP1 (IN)                                             | https://api.app.eu2.coralogix.com/api/v1/external/integrations/ |

#### Accordion
<div class="accordion-wrapper" markdown="1">
<div class="accordion-item" markdown="1">
<div class="accordion-title">
Dropdown 1
</div>

<div class="accordion-content" id="dropdown1" markdown="1">
Lorem ipsum dolor sit amet, consectetur adipisicing elit. Commodi consequatur deleniti excepturi facilis illo ipsam obcaecati omnis sapiente suscipit, tenetur! Alias consectetur debitis dolor dolore itaque maiores pariatur quis sapiente?
[Coralogix AWS Resource Metadata Collection](aws/aws-resource-metadata-collection/index.md)
<button class="md-clipboard md-icon" title="Copy to clipboard" data-clipboard-target="#dropdown1"></button>
</div>
</div>

<div class="accordion-item">
<div class="accordion-title">
Dropdown 2
</div>

<div class="accordion-content" id="dropdown2">
Lorem ipsum dolor sit amet, consectetur adipisicing elit. Commodi consequatur deleniti excepturi facilis illo ipsam obcaecati omnis sapiente suscipit, tenetur! Alias consectetur debitis dolor dolore itaque maiores pariatur quis sapiente?
<button class="md-clipboard md-icon" title="Copy to clipboard" data-clipboard-target="#dropdown2"></button>
</div>
</div>
</div>

#### List group
<div class="list-group">
    <a class="list-group-item" href="{{ config.site_url }}user-guides/monitoring-and-insights/explore-screen/create-and-manage-saved-views/">1. Create & Manage Saved Views</a>
    <a class="list-group-item" href="{{ config.site_url }}user-guides/monitoring-and-insights/logs-screen/logs-in-explore-screen/">2. Logs in Explore Screen</a>
    <a class="list-group-item" href="{{ config.site_url }}user-guides/monitoring-and-insights/explore-screen/custom-views/">3. Custom Views</a>
    <a class="list-group-item" href="{{ config.site_url }}user-guides/monitoring-and-insights/explore-screen/content-column/">4. Content Column</a>
    <a class="list-group-item" href="{{ config.site_url }}user-guides/monitoring-and-insights/explore-screen/manage-keys/">5. Manage Keys</a>
</div>

#### List
::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header
            Add. data 1
        ::: list-item-content
            Verify you are signed into your cloud provider with your credentials 1.
    ::: custom-list-item marker=2
        ::: list-item-header
            Add. data 2
        ::: list-item-content
            **Verify** you are signed into your cloud provider with your credentials.
            ![](../integrations/gcp/gcp-getting-started/images/google-workspace20-accounts.jpg)
    ::: custom-list-item marker=3
        ::: list-item-header
            Add. data 3
        ::: list-item-content
            Verify you are signed into your cloud provider with your credentials 3.

#### Code Numbered
``` c++ linenums="1"
#include <iostream>
using namespace std;

int main() {
  int n1, n2, hcf;
  cout << "Enter two numbers: ";
  cin >> n1 >> n2;

  // swapping variables n1 and n2 if n2 is greater than n1.
  if ( n2 > n1) {   
    int temp = n2;
    n2 = n1;
    n1 = temp;
  }
    
  for (int i = 1; i <=  n2; ++i) {
    if (n1 % i == 0 && n2 % i ==0) {
      hcf = i;
    }
  }

  cout << "HCF = " << hcf;

  return 0;
}
```

#### Code 2
``` ts
import { promises as fs } from 'fs';

export class Store {
    constructor() {
    }

    async foo() {
        const buffer = await fs.readFile('test.yaml', 'utf-8');
        return buffer.substr(0, buffer.length);
    }
}
```

#### Code 3
``` python
# This function adds two numbers
def add(x, y):
    return x + y

# This function subtracts two numbers
def subtract(x, y):
    return x - y

# This function multiplies two numbers
def multiply(x, y):
    return x * y

# This function divides two numbers
def divide(x, y):
    return x / y


print("Select operation.")
print("1.Add")
print("2.Subtract")
print("3.Multiply")
print("4.Divide")

while True:
    # take input from the user
    choice = input("Enter choice(1/2/3/4): ")

    # check if choice is one of the four options
    if choice in ('1', '2', '3', '4'):
        try:
            num1 = float(input("Enter first number: "))
            num2 = float(input("Enter second number: "))
        except ValueError:
            print("Invalid input. Please enter a number.")
            continue

        if choice == '1':
            print(num1, "+", num2, "=", add(num1, num2))

        elif choice == '2':
            print(num1, "-", num2, "=", subtract(num1, num2))

        elif choice == '3':
            print(num1, "*", num2, "=", multiply(num1, num2))

        elif choice == '4':
            print(num1, "/", num2, "=", divide(num1, num2))
        
        # check if user wants another calculation
        # break the while loop if answer is no
        next_calculation = input("Let's do next calculation? (yes/no): ")
        if next_calculation == "no":
          break
    else:
        print("Invalid Input")
```

#### Table
| Datadog Service           | Pricing Structure                 | Price                                                             |
|---------------------------|-----------------------------------|-------------------------------------------------------------------|
| Log Ingestion             | Per ingested GB, per month        | Datadog Service                                                   |
| Log Retention (30 days)   | Per million log events, per month | $0.10                                                             |
| APM                       | Per host, per month               | $31 - $45 (range reflects different tiers and access to features) |
| Infrastructure Monitoring | Per host, per month               | $15 - $34 (range reflects different tiers and access to features) |
| Database Monitoring       | Per database host, per month      | $70                                                               |
| RUM                       | Per 1,000 sessions, per month     | $1.50                                                             |

#### Additional content
|               |                                                                                                                                                                                                                                                                    |
|---------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Documentation | [Archive Retention Policy](../user-guides/data-flow/s3-archive/archive-retention-policy/index.md)<br/>[TCO Optimizer](../user-guides/account-management/tco-optimizer/logs/index.md)<br/>[TCO Optimizer API](../developer-portal/apis/tco-controls/tco-optimizer-http-api/index.md) |

<!--- Without header -->
::: additional-content items="id: 7gbeX0k1Z5Y, title: Title 1; id: Emk93jV8cjg, title: Title 2; id: NFo_v0NKxcg, title: Title 3"

<!--- With header -->
::: additional-content items="id: Emk93jV8cjg; id: NFo_v0NKxcg; id: 7gbeX0k1Z5Y" show_header

#### Notes
!!! note

    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nulla et euismod
    nulla. Curabitur feugiat, tortor non consequat finibus, justo purus auctor
    massa, nec semper lorem quam in massa.

!!! warning

    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nulla et euismod
    nulla. Curabitur feugiat, tortor non consequat finibus, justo purus auctor
    massa, nec semper lorem quam in massa.

## Alerts Notification Source Type Schema  

### General reference

::: json-schema
    ::: json-schema-item title="alert" description="Represents the alert details, including timestamp, ID, status, and group information"
        ::: json-schema-prop title="alertDataUrl"
            Link to the incident page (e.g., https://coralogix.com/#/insights?id=76c411be-gg4d-4fb1-a987-5fce042deaaf)
        ::: json-schema-prop title="groupingKey"
            Used as the deduplication key
        ::: json-schema-item title="groups" description="List of alert groups associated with the alert (including priority and key-values)."
            ::: json-schema-item title="groups[0]"
                ::: json-schema-item title="keyValues" description="Key-value pairs associated with the alert group"
                    ::: json-schema-item title="resource"
                        ::: json-schema-item title="attributes"
                            ::: json-schema-prop title="Team"
                                Team field description
                    ::: json-schema-item title="coralogix"
                        ::: json-schema-item title="metadata"
                            ::: json-schema-prop title="subsystemName"
                                subsystemName field description
