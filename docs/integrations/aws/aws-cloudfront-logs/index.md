---
title: "AWS CloudFront Logs"
date: "2024-06-05"
coverImage: "AWS-1000X1000.png"
description: "Enable logging from your Amazon CloudFront distribution to seamlessly send web access logs to Coralogix using the AWS CloudFront Logs via Firehose integration package."
---

Enable logging from your Amazon CloudFront distribution to seamlessly send web access logs to Coralogix using the **AWS CloudFront Logs** integration packages.

## Overview

Choose between standard logging and real-time logs to send CloudFront access logs to Coralogix seamlessly.

- **Standard logs**: Sent to an S3 bucket and then to Coralogix using an AWS Lambda trigger. Follow [Amazon CloudFront docs](https://docs.aws.amazon.com/AmazonCloudFront/latest/DeveloperGuide/AccessLogs.html) and [this Coralogix guide](../forward-aws-logs-via-lambda-shipper/index.md) to enable standard logging.

- **Real-time logs**: Instantly dispatched using Kinesis Data Streams and Firehose. Refer to [Amazon CloudFront docs](https://docs.aws.amazon.com/AmazonCloudFront/latest/DeveloperGuide/real-time-logs.html) and [this Coralogix guide](../amazon-data-firehose/send-logs-using-amazon-data-firehose/index.md) to activate real-time logging.

Utilize our [extension packages](../../../user-guides/getting-started/packages-and-extensions/extension-packages/index.md) to automate CloudFront access logs parsing for immediate insights.

## Enable standard logging

Ensure you have set up an [AWS S3 bucket](../../../user-guides/data-flow/s3-archive/connect-s3-archive/index.md).

### Configure standard logging

1. Navigate to **Services > All Services > CloudFront** in the AWS Management Console.

3. Click on your distribution's **ID** and select **Edit** in the Setting section under the General tab.

5. Toggle **On** in the **Standard logging** section and configure the settings.

7. Choose your S3 bucket for **S3 bucket**.

9. Optionally, add a log prefix like `cloudfront_logs`.

11. Click **Save changes.**

## Enable real-time logs

### Create a Kinesis data stream

Set up real-time logging:

1. Go to **Services > Kinesis** in the AWS Management Console.

3. In the AWS Management Console, choose **Services > Kinesis**.

5. Select **Data streams**, and then **Create data stream**.

7. Enter a **Data stream name**. For example, `CloudFront-DataStream`.

9. Select a Data Stream capacity mode of your choice.

11. Select **Create data stream**.

13. In the **Consumers** section, select **Process with delivery stream**.

15. For the **Destination**, select **Coralogix**.

17. Enter a **Delivery stream name**. For example, `CloudFront-DeliveryStream`.

19. In the **Destination settings** section, for the HTTP endpoint URL, select **Coralogix logs - US** or **Coralogix logs - EU**.

21. For **API key**, enter the API key for your Coralogix account.

23. Select **Add parameter**.

25. In the **Backup settings** section, you may choose either **Failed data** only or **All data** for the **Source record backup in Amazon S3** option, based on your preference.

27. For the S3 backup bucket, select **Browse** to search for and select the S3 bucket name you created above for storing CloudFront logs.

29. Choose **Create delivery stream**.

### Create a logging configuration

Next, create a real-time logging configuration and attach it to your CloudFront distribution:

1. In the AWS Management Console, choose **Services > CloudFront**.

3. In the **Telemetry section** on the left, select **Logs**.

5. Click on the **Real-time configurations** tab, and then choose **Create configuration**.

7. Enter a configuration name. For example: `CloudFront-RealTimeLogs`.

9. Enter a sampling rate from `1` to `100`.

11. For **Fields**, select **All fields** or choose the fields you wish to include in your logs.

13. For **Endpoint**, select the Data Stream name you created in Step 3. For example: `CloudFront-DataStream`.

15. In the **Distributions** section, select your CloudFront distribution ID.

17. For **Cache behaviors**, check **Default (\*)**.

19. Choose **Create configuration**.

Within a couple of minutes, you should begin to see your logs in our [Explore Screen](../../../user-guides/monitoring-and-insights/logs-screen/logs-in-explore-screen/index.md).

## Deploy the integration package

Deploy the relevant CloudFront integration package:

- For **standard logging**, select the **AWS CloudFront Logs** **with S3** integration package.

- For **real-time logging**, select the **AWS CloudFront Logs via Firehose** integration package.

Take these steps:

1. Navigate to **Data Flow** > **Integrations** from your Coralogix toolbar. Select the AWS CloudFront Logs integration package that suits your needs, then click **Connect**.

3. Click **Add New.**

5. Input the integration **Settings.**
    - **Name**.
    
    - **API key**. Your Coralogix [Send-Your-Data API key](../../../user-guides/account-management/api-keys/send-your-data-api-key/index.md).
    
    - **Application name**. The desired application name in the Coralogix platform. If not set, it will default to the delivery stream name.
    
    - **Subsystem name**. The desired subsystem name in the Coralogix platform. If not set, it will default to the ARN.
    
    - **Kinesis Stream ARN**. CloudFront real-time logs are delivered to the data stream of your choice in Amazon Kinesis Data Streams. If using a stream as a source for logs, enter the ARN.
    
    - **AWS region**. The name of the AWS region with data to collect.
    
    - **Advanced - CloudWatch Retention Days.** Select the number of days to retain the logs in CloudWatch log groups.

7. Click **Next**.

9. You will be rerouted to the website for the integration. Verify that all of the auto-pre-populated values are correct and click **Create CloudFormation.**

11. Revert back to the Coralogix application and click **Complete** to ensure your deployment is successful. This triggers a test to verify the deployment, the result of which can be seen on the next page as either **Failed** or **Connected.**

## Real-time log parsing

Our built-in parsing rule for real-time logs assumes that all fields will be logged. For the built-in parsing rules to work and to populate data in the widgets of the quickstart dashboard provided, all fields need to be logged.

If you choose only to log a subset of the fields, you must define a custom parsing rule that matches your log format. This is required for the logs to parse correctly.

Enjoy our pre-built dashboard with the AWS Edge Insights extension package. To start, navigate to **Data Flow** > **Extensions** > **AWS Edge Insights** in your Coralogix toolbar.

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by emailing [<EMAIL>](mailto:<EMAIL>).
