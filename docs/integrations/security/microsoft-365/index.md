---
title: "Microsoft 365"
date: "2024-02-04"
description: "Microsoft 365 provides detailed audit logs of user activities, such as file downloads, data access grants, configuration changes, and DLP event logs. You can monitor the logs in the Coralogix platform."
---

## Overview

Microsoft 365 provides detailed audit logs of user activities, such as file downloads, data access grants, configuration changes, and DLP event logs. 

You can monitor the logs in the Coralogix platform to:

- Track user activities, such as login attempts, file access, and changes to permissions.

- Provide a record of actions taken by users to demonstrate compliance with different regulations.

- Detect unexpected changes to files or settings to diagnose and resolve problems.

- Keep a record of actions like data deletions or modifications to ensure recovery of lost or altered information and maintain data integrity.

The procedure below explains how to configure the integration, allowing you to read logs from Microsoft 365 into Coralogix.

## Prerequisites

### Enable auditing in Microsoft 365

Verify that auditing is enabled in Microsoft 365. If not, follow this procedure.

1. Log into the M365 platform and navigate to the **Admin** tab.

    ![](images/image.png)

1. In a new window, click **Security**.

    ![](images/image%201.png)

1. Expand **Search**, then click **Audit log search**.

    ![](images/image%202.png)

1. If the **Audit logs** option is disabled,  a blue banner will be displayed on top of the page, click it to enable the audit logs.

    ![](images/image%203.png)

### Configure Coralogix application in Microsoft Entra

Verify that Coralogix enterprise application has been configured in Microsoft Entra. If not, follow this procedure.

1. Log into Azure portal.

2. Navigate to Entra (formerly known as **Azure Active Directory**).

3. Select **Enterprise applications**, create a new application and register it.

4. Navigate to **App registrations**.

5. Select the application you have just created, and:

    1. Configure a secret key

    2. Click **API Permissions**, select **Office 365 Management APIs**. 

    3. Add these 3 permissions and click **Grant admin consent for Coralogix**.
    
         ![](images/image%204.png)
    
    4. Navigate to **Certificate & secrets**.

    5. Create a client secret and copy it into the clipboard.

## Configure a Microsoft 365 integration

1. In the Coralogix UI, go to **Data Flow > Integrations**.

2. From the **Integrations** section, select **Microsoft 365**.

3. Enter configuration parameters according to your application requirements:

    - Integration name - Meaningful name of the M365 integration.

    - Application name - The Coralogix application name.

    - Subsystem name - The Coralogix subsystem name.

    - Tenant ID - Your Microsoft tenant ID.

    - Application ID - Application ID used to authenticate and read logs from your Microsoft 365 environment.

    - Secret value -  Secret value used to authenticate and read logs  from your Microsoft 365 environment.

4. Click **Create** to create the integration.

![](images/image%205.png)

## Limitations

If logs are sent with a delay of more than 24 hours, their metadata timestamp will reflect the ingestion time instead of the original event time. As a result, certain Coralogix features, such as alerts, may not operate correctly on these delayed logs. The original event time is retained in a separate field within the log, with the field name varying based on the logging scheme of each vendor.

Some duplication of audit logs is expected, as explained in the [Microsoft documentation](https://learn.microsoft.com/en-us/office/office-365-management-api/troubleshooting-the-office-365-management-activity-api#frequently-asked-questions-about-the-office-365-management-activity-api):
This duplication of events is an expected and designed behavior intended to prevent the loss of any audit events.

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Contact us **via our in-app chat** or by emailing [<EMAIL>](mailto:<EMAIL>).
