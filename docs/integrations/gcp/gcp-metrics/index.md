---
title: "GCP Metrics"
date: "2023-08-17"
coverImage: "icon_cloud_192pt_clr-1.png"
description: "Google Cloud Platform provides built-in monitoring and observability tools that allow users to collect and analyze metrics and traces from their GCP resources. Send Google Cloud metrics seamlessly to Coralogix. Search, analyze, and visualize your data, gaining insights into application behavior, identifying errors, and troubleshooting problems."
---

## Overview

Google Cloud Platform provides built-in monitoring and observability tools that allow users to collect and analyze metrics from their GCP resources. Send **Google Cloud metrics** seamlessly to Coralogix. Search, analyze, and visualize your data, gaining insights into application behavior, identifying errors, and troubleshooting problems.

## Prerequisites

[Configure a service account](../gcp-getting-started/index.md)  to facilitate automated intermediation.

## Setup

An integration must be created to start collecting metrics for a GCP project.

**STEP 1.** From your Coralogix toolbar, navigate to **Data Flow** > **Extensions**.

**STEP 2.** From the **Integrations** section, select **GCP Metrics**.

![](images/GCP-Metrics-Add-New-1024x652.png)

**STEP 3.** Click **\+ ADD NEW**.

**STEP 4.** Pick the **ACCOUNT KEY/IMPERSONATION** authentication flow and click **NEXT** (available for version **1.1.0** and later).

**STEP 5.** (For key-based authentication only) If you haven’t already done so, click **GO TO GCP ACCOUNT** and create a key file as described above. Once you have created a key file, click **NEXT**.

**STEP 6.** (For key-based authentication only) Click **SELECT FILE** and select the key file that you created in the previous section. A confirmation that the file was uploaded successfully will appear. Click **NEXT**.

**STEP 7.** Choose the metric prefixes you want to pull into Coralogix by selecting them from the dropdown menu. To limit unnecessary API calls that fetch no data but count towards the quota limit, specify only the GCP prefixes of the metrics you want to pull into Coralogix.

!!! Note

    - Coralogix queries only the metrics under the selected prefixes.

    - For metrics for which no data has been available for more than 15 minutes, the scraping interval will be reduced from 15 minutes to 1 hour.

    - Querying all of your metrics may result in GCP quota limits and less frequent metric updates.

**STEP 8.** Click **NEXT**.

**STEP 9.** Select the application and subsystem names.

- Enter a default name for the application, or \[optionally\] select labels that will be used to create the application name. The first label value, which matches a metric label, will be used as the application_name. For example, given three application labels, if the first does not match any metrics, the value of the second label will be used as the application name.

- Enter a default subsystem name or \[optionally\] select labels that will be used to create the subsystem name.

- **Note:** Application and Subsystem names for metrics are optional. If no default name or labels are provided, the application name and subsystem name will not be populated for this metric.

- **GCP Project ID**: ID of a GCP project that you perform the setup for (for impersonation-based authentication only).

- **Service Account Email**: Email of the service account you configured at the beginning (for impersonation-based authentication only).


**Step 10.** (For impersonation-based authentication only) 

- Click **NEXT**

- Copy email of Coralogix principal

- Click **GO TO GCP ACCOUNT**

- Select the service account you created earlier. Navigate to the **PERMISSIONS** tab, click **GRANT ACCESS** and paste the copied email into the **NEW PRINCIPAL** field. Assign the `Service Account Token Creator` role and click **SAVE**.

- Navigate back to the Coralogix portal. 

!!! Note
    It may take a few minutes for the GCP IAM role change to take effect.

**Step 11.** Click **COMPLETE**.

**Notes**:

- Currently, only GCP resource labels are supported. These labels vary based on the GCP Resource Type. For example, Compute Engine VM instances include labels such as `instance_id` and `zone`, while CloudSQL Database instances include labels such as `database_id` and `region`. Custom user-defined labels are not supported and will not be visible in Coralogix.

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email at [<EMAIL>](mailto:<EMAIL>).
