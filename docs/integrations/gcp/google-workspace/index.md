---
title: "Google Workspace"
date: "2024-06-05"
coverImage: "GCP_large.png"
description: "Google Workspace audit logs provide detailed records of user activities, such as logins, file sharing, and administrative actions. These logs help you understand how Google Workspace applications are being used, track changes, and monitor for suspicious behavior, enabling you to maintain a secure and efficient workspace."
---

## Overview

Google Workspace audit logs provide detailed records of user activities, such as logins, file sharing, and administrative actions. These logs help you understand how Google Workspace applications are being used, track changes, and monitor for suspicious behavior, enabling you to maintain a secure and efficient workspace.

Send audit logs from Google Workspace applications to Coralogix to enhance visibility into performance and usage patterns. By utilizing the Google Workspace integration, you can proactively resolve issues and optimize your Google Workspace environment, ensuring improved reliability, security, and efficiency.

## Prerequisites

- [Super admin permissions](https://support.google.com/a/answer/2405986?hl=en) in Google Cloud

- An existing project within your Google Cloud

## Required permissions

Users with the following permissions may view and/or manage integrations.

| Resource | Action | Description | Explanation |
| --- | --- | --- | --- |
| `integrations` | `ReadConfig` | View Deployed Integrations | View deployed integration packages. |
| `integrations` | `Manage` | Manage Integrations | Deploy, undeploy, and update integrations. |

Find out more about roles and permissions [here](../../../user-guides/account-management/user-management/create-roles-and-permissions/index.md).

## Setup

**STEP 1.** [Configure a Service Account](../gcp-getting-started/index.md) to facilitate automated intermediation.

**STEP 2**. Set up [Domain Wide Delegation](https://developers.google.com/identity/protocols/oauth2/service-account#delegatingauthority), to authorize your Service Account to read user data and send it to Coralogix. The OAuth Scope permissions required are as follows:

`https://www.googleapis.com/auth/admin.directory.user.readonly`

`https://www.googleapis.com/auth/admin.reports.audit.readonly`

**STEP 3**. Navigate to **API & Services** > **Library screen**. Select **Admin SDK API** and ensure it’s enabled.

**STEP 4**. From your Coralogix toolbar, navigate to **Data Flow** > **Integrations**. Select **Google Workspace Users**.

**STEP 5**. Click **\+ ADD NEW**.

**STEP 6**. Pick the **ACCOUNT KEY/IMPERSONATION** authentication flow and click **NEXT** (available for version **0.2.0** and later).

**STEP 7**. (For key-based authentication only) If you haven’t already done so in STEP 1, click **GO TO GCP ACCOUNT** and create a key file. Then, click **NEXT**.

**STEP 8**. (For key-based authentication only) Click **SELECT FILE** and upload the key file **you previously created**. A confirmation will appear when the file is uploaded successfully. Click **NEXT**.

**STEP 9**. Fill in the settings:

- **Integration name:** Enter a name for your integration. This field is automatically populated, but can be changed if you want.

- **Application name**: Enter the "Application Name".

- **Subsystem name**: The "Subsystem Name" will default to "Google Workspace" but may be modified.

- **Applications**: Select the Google Workspace applications from the dropdown menu for which you would like to send audit logs to Coralogix.

- **Impersonated email:** Enter a valid email address of a super admin user to be impersonated.

- **GCP Project ID**: ID of a GCP project that you perform the setup for (for impersonation-based authentication only).

- **Service Account Email**: Email of the service account you configured at the beginning (for impersonation-based authentication only).


**Step 10.** (For impersonation-based authentication only)

- Click **NEXT**

- Copy the email of the Coralogix principal

- Click **GO TO GCP ACCOUNT**

- Select the service account you created earlier, go to the **PERMISSIONS** tab, click **GRANT ACCESS**, paste the copied email into the **NEW PRINCIPAL** field, assign the `Service Account Token Creator` role and click **SAVE**

- Navigate back to the Coralogix portal

!!! Note 
    It may take a few minutes for the GCP IAM role change to take effect.

**STEP 11**. Click **COMPLETE** to finish the setup. It will take several minutes for the integration to take effect and for user data to be available.

## Monitoring Google Workspace logs

Explore these use cases for effectively monitoring Google Workspace logs in the Coralogix platform.

### View all user activities in Google Workspace

To view all activities performed by the user "<EMAIL>" in Google Drive over the last 7 days, select the Application Name and Subsystem Name for Google Workspace integration. In the [Explore Screen](../../../user-guides/monitoring-and-insights/logs-screen/logs-in-explore-screen/index.md), apply the following query within the last 7-day timeframe:

```
<ActorEmail:"<EMAIL>>" AND ApplicationName:"drive"
```

### Alert on suspicious activity

To detect if a user downloads an unusual amount of files from Google Drive, set an alert for when a user downloads more than 5 files within 10 minutes. Configure a standard alert with the following query:

``` 
ApplicationName:"drive" AND Event.Name:"download"
```

Set the threshold to at least 5 logs in 10 minutes and group by `ActorEmail`.

![](images/image_26.png)

### View most active Google Workspace applications

To see statistics about the usage of your Google Workspace applications, [create a horizontal bar chart widget](../../../user-guides/custom-dashboards/widgets/horizontal-bar-charts/index.md) in [Custom Dashboards](../../../user-guides/custom-dashboards/getting-started/index.md). Set the widget group-by parameter to `Application.Name` to display the top applications in Google Workspace.

![](images/image-27-1.png)

## Limitations

**Important:** Note that audit logs retrieved via the Reports API are not available in real time. You should account for a potential delay of up to 24 hours before all log data becomes accessible.

If logs are sent with a delay of more than 24 hours, their metadata timestamp will reflect the ingestion time instead of the original event time. As a result, certain Coralogix features, such as alerts, may not operate correctly on these delayed logs. The original event time is retained in a separate field within the log, with the field name varying based on the logging scheme of each vendor.

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to contact us **via our in-app chat** or by emailing [<EMAIL>](mailto:<EMAIL>).
