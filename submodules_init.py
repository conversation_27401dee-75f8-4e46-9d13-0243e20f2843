import json
import subprocess
import os
import re
import requests
import configparser
import shutil

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))

def add_submodules_from_gitmodules():
    gitmodules_path = os.path.join(SCRIPT_DIR, ".gitmodules")
    if not os.path.isfile(gitmodules_path):
        print(f"{gitmodules_path} not found.")
        return

    config = configparser.ConfigParser()
    config.read(gitmodules_path)

    for section in config.sections():
        if section.startswith("submodule"):
            path = config[section].get("path")
            url = config[section].get("url")
            if not os.path.isdir(path):
                print(f"Adding submodule: {path} from {url}")
                run_bash_command(f"git submodule add -f {url} {path}")
            else:
                print(f"Submodule already exists: {path}")

# This script initializes git submodules and processes markdown files from external repositories.
# It handles special HTML comment tags used for documentation processing.
# HTML comments are used instead of custom markdown comment tags to be compatible with mdox linter.

def run_bash_command(command):
    try:
        print(f"Running command: {command}")
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True,
            cwd=SCRIPT_DIR
        )
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        print(f"Command running error: {e}")
        return None

def get_repo_namespace(submodule_path):
    config_key = f"submodule.{submodule_path}.url"
    repo_url = run_bash_command(f"git config --file .gitmodules --get {config_key}")

    if repo_url:
        match = re.search(r'github\.com[:/](.*?)/(.*?)(\.git)?$', repo_url)
        if match:
            namespace = match.group(1)
            return namespace
    return None

def extract_block(content, start_marker, end_marker):
    # Extract content between HTML comment tags
    # For example: <!-- tag --> content <!-- /tag -->
    pattern = rf'<!-- {start_marker} -->(.*?)<!-- {end_marker} -->'
    match = re.search(pattern, content, re.DOTALL)
    return match.group(1).strip() if match else ""

def extract_descriptions(content):
    # Extract description blocks with their attributes from HTML comments
    # Format: <!-- description id="ID" title="TITLE" examples_path="PATH" --> content <!-- /description -->
    descriptions = []

    pattern = r'<!-- description id="(.*?)" title="(.*?)" examples_path="(.*?)" -->(.*?)<!-- /description -->'

    for match in re.finditer(pattern, content, re.DOTALL):
        descriptions.append({
            "id": match.group(1),
            "title": match.group(2),
            "examples_path": match.group(3),
            "content": match.group(4).strip()
        })

    return descriptions

def extract_example(content, example_id):
    # Extract example blocks with specific ID from HTML comments
    # Format: <!-- example id="ID" --> content <!-- /example -->
    pattern = rf'<!-- example id="{example_id}" -->(.*?)<!-- /example -->'
    match = re.search(pattern, content, re.DOTALL)
    return match.group(1).strip() if match else ""

def fetch_file_from_github(url):
    try:
        response = requests.get(url)
        if response.status_code == 200:
            return response.text
        else:
            print(f"Failed to fetch {url}, status code: {response.status_code}")
            return None
    except requests.RequestException as e:
        print(f"Error fetching {url}: {e}")
        return None

def convert_github_callouts_to_mkdocs_style(content: str) -> str:
    # Convert GitHub-style callouts to MkDocs admonition format
    # For example: > [!NOTE] becomes !!! note
    lines = content.splitlines()
    result = []
    i = 0

    callout_start_pattern = re.compile(r"^>\s*\[!(NOTE|TIP|IMPORTANT|WARNING|CAUTION)\]\s*$", re.IGNORECASE)
    callout_line_pattern = re.compile(r"^>\s?(.*)$")

    while i < len(lines):
        line = lines[i]
        match = callout_start_pattern.match(line.strip())
        if match:
            tag = match.group(1).lower()
            result.append(f"!!! {tag}\n")
            i += 1
            while i < len(lines):
                next_line = lines[i]
                stripped = next_line.strip()
                if stripped == "":
                    result.append("")
                    i += 1
                    break
                match_line = callout_line_pattern.match(next_line)
                if match_line:
                    result.append(f"    {match_line.group(1)}")
                else:
                    result.append(f"    {next_line}")
                i += 1
        else:
            result.append(line)
            i += 1

    return "\n".join(result)

def extract_split_blocks(content, base_dir):
    # Parse split blocks and create files with the corresponding content
    # Format: <!-- split title="..." path="..." --> ... <!-- /split -->
    split_pattern = re.compile(
        r'<!-- split(?=[^>]*path="([^"]+)")(?:(?:[^>]*title="([^"]*)")?)?[^>]*-->(.*?)<!-- /split -->',
        re.DOTALL
    )
    for match in split_pattern.finditer(content):
        rel_path = match.group(1).strip()
        title = match.group(2)
        block_content = match.group(3).strip()
        abs_path = os.path.join(base_dir, rel_path)
        os.makedirs(os.path.dirname(abs_path), exist_ok=True)
        with open(abs_path, 'w', encoding='utf-8') as f:
            if title and title.strip():
                f.write(f"# {title.strip()}\n\n{block_content}\n")
            else:
                f.write(f"{block_content}\n")
        print(f"[split] Created: {abs_path}")

def process_file(file_path, repo_url, doc):
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()

        # Remove content within delete blocks
        # Format: <!-- delete --> content to be removed <!-- /delete -->
        content = re.sub(r'<!-- delete -->(.*?)<!-- /delete -->', '', content, flags=re.DOTALL)

        # Update static paths
        content = re.sub(r'(\./static/(.*?))', rf'({repo_url}{os.path.dirname(doc)}/static/\1)', content)

        content = convert_github_callouts_to_mkdocs_style(content)

        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(content)

        print(f"Processed {file_path}")

        base_dir = os.path.dirname(file_path)

        extract_split_blocks(content, base_dir)
        start_content = extract_block(content, "static-modules-readme-start-description", "/static-modules-readme-start-description")
        descriptions = extract_descriptions(content)
        end_content = extract_block(content, "static-modules-readme-end-description", "/static-modules-readme-end-description")

        for desc in descriptions:
            example_url = f"{repo_url}/{desc['examples_path'].strip('/')}"
            example_content = fetch_file_from_github(example_url)
            if example_content:
                example_content = extract_example(example_content, desc["id"])
            else:
                example_content = ""

            main_doc_filename = os.path.basename(doc)

            additional_config_line = f"\nFor additional configuration options, please refer to our main [Terraform documentation](../{main_doc_filename}).\n"

            generated_content = f"""
# {desc['title']}

{start_content}

{desc['content']}

{example_content}

{end_content}

{additional_config_line}
""".strip()

            output_path = os.path.join(base_dir, "examples", f"{desc['id'].lower()}.md")
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            with open(output_path, 'w', encoding='utf-8') as output_file:
                output_file.write(generated_content)

            print(f"Generated file: {output_path}")

    except FileNotFoundError:
        print(f"File {file_path} not found.")
    except Exception as e:
        print(f"Error processing {file_path}: {e}")

def get_default_branch(submodule_path):
    branch = run_bash_command(f"git -C {submodule_path} symbolic-ref --short HEAD")
    if not branch:
        branch = "master"
    return branch

def main():
    # Main function to process submodules based on configuration in submodules.json
    json_file_path = os.path.join(SCRIPT_DIR, "submodules.json")

    try:
        add_submodules_from_gitmodules()

        with open(json_file_path, 'r') as file:
            data = json.load(file)

        # Initialize and update all git submodules
        run_bash_command("git submodule update --init --recursive")

        for item in data:
            submodule_path = item["submodule_path"]
            docs = item["docs"]

            branch = get_default_branch(submodule_path)

            # Reset the submodule to the latest state on master branch
            run_bash_command(f"git -C {submodule_path} reset --hard")
            run_bash_command(f"git -C {submodule_path} checkout {branch}")
            run_bash_command(f"git -C {submodule_path} pull origin {branch}")

            # Set up sparse checkout to only get the needed documentation files
            # This reduces the amount of data that needs to be downloaded
            run_bash_command(f"git -C {submodule_path} sparse-checkout init")

            sparse_checkout_file = f".git/modules/{submodule_path}/info/sparse-checkout"
            sparse_content = "\n".join(docs)
            with open(sparse_checkout_file, "w") as sparse_file:
                sparse_file.write(sparse_content)

            run_bash_command(f"git -C {submodule_path} sparse-checkout reapply")

            # Determine the GitHub repository URL for raw content access
            namespace = get_repo_namespace(submodule_path)
            repo_name = submodule_path.split('/')[-1]

            if namespace:
                repo_url = f"https://raw.githubusercontent.com/{namespace}/{repo_name}/{branch}"
            else:
                print(f"Could not determine namespace for {submodule_path}, skipping...")
                continue

            # Process each documentation file specified in the configuration
            for doc in docs:
                file_path = os.path.join(submodule_path, doc.strip('/'))
                new_file_path = file_path

                if '/docs/' in file_path:
                    new_file_path = file_path.replace('/docs/', '/documents/')
                    os.makedirs(os.path.dirname(new_file_path), exist_ok=True)
                    shutil.move(file_path, new_file_path)

                process_file(new_file_path, repo_url, doc)

    except FileNotFoundError:
        print(f"File {json_file_path} not found.")
    except json.JSONDecodeError as e:
        print(f"JSON parsing error: {e}")

if __name__ == "__main__":
    main()