{% extends "base.html" %}

{% include "extrahead.html" %}

{% import "partials/get-started-card.html" as get_started_card with context %}
{% import "partials/popular-docs-card.html" as popular_docs_card with context %}

{% block content %}
  {% include "partials/welcome-banner.html" %}

  <div class="page home-page">
    <div class="home-page-inner">
      <div class="get-started-section">
        <h2 class="home-section-header">Get started in four easy steps</h2>

        <div class="get-started-cards-wrapper">
          {% for card in config.extra.get_started_items %}
            {{ get_started_card.render(card, loop.index) }}
          {% endfor %}
        </div>
      </div>

      <div class="popular-docs-section">
        <div class="popular-docs-inner">
          <div class="section-essentials">Essentials</div>

          <h2 class="home-section-header">Popular docs</h2>

          <div class="home-section-description">
            Everything you need to know about our platform. Can’t find the answer
            you’re looking for? Feel free to contact us
          </div>

          <div class="popular-docs-cards-wrapper">
            {% for card in config.extra.popular_docs %}
              {{ popular_docs_card.render(card) }}
            {% endfor %}
          </div>
        </div>
      </div>
    </div>
  </div>
  {% include "partials/nav-home.html" %}
{% endblock %}
