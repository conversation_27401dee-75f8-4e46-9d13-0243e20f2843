{% extends "base.html" %}

{% set external_doc_meta = config.extra.external_docs_meta[page.file.src_uri] %}
{% if external_doc_meta %}
  {% set _ = page.__setattr__('meta', { 'title': page.title }) %}
  {% set _ = page.__setattr__('meta', external_doc_meta) %}
{% endif %}

{% include "extrahead.html" %}

{% block container %}
<div
  class="md-content"
  data-md-component="content"
  data-page-global-category="{{ page.meta.global_category }}"
  data-site-url="{{ config.site_url }}"
>
  {% include "parent-section.html" %}

  {% if page.meta and page.meta.parent_sections %}
    {% for nav_item in nav %}
      {% if nav_item.title == page.meta.parent_sections | first %}
        {% set first_child = nav_item.children | first %}
        {% if first_child.meta %}
          <script>
            document.documentElement.style.setProperty("--nav-parent-section-icon-url", "url({{ first_child.meta.icon_url }})");
          </script>
        {% endif %}
      {% endif %}
    {% endfor %}
  {% else %}
    {% set first_section = page.ancestors | reverse | first %}
    {% if first_section %}
      {% set first_child = first_section.children | first %}
      {% if first_child.meta %}
        <script>
          document.documentElement.style.setProperty("--nav-parent-section-icon-url", "url({{ first_child.meta.icon_url }})");
        </script>
      {% endif %}
    {% endif %}
  {% endif %}

  <article class="md-content__inner md-typeset">
    {% block content %}
      {% include "partials/toc-mobile.html" %}
      {% include "partials/breadcrumbs.html" %}
      {% include "partials/content.html" %}
    {% endblock %}
  </article>
</div>
{% include "partials/nav-home.html" %}
{% endblock %}