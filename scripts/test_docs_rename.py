#!/usr/bin/env python3
# Тестовий скрипт для перевірки правильності перейменування /docs/ → /documents/
# Це важливо для сумісності з хостингом

import os
import json
from pathlib import Path

def test_docs_rename():
    # Тестує правильність перейменування файлів /docs/ на /documents/
    print("🧪 Тестуємо перейменування /docs/ → /documents/...")

    external_dir = Path("docs/external")

    if not external_dir.exists():
        print("❌ Зовнішні репозиторії не знайдено")
        return False

    all_tests_passed = True

    for repo_dir in external_dir.iterdir():
        if not repo_dir.is_dir():
            continue

        metadata_file = repo_dir / ".repo_metadata.json"

        if not metadata_file.exists():
            continue

        with open(metadata_file) as f:
            metadata = json.load(f)

        original_files = metadata.get('original_files', [])
        actual_files = metadata.get('actual_files', [])

        print(f"\n📁 Testing {repo_dir.name}:")

        # Check for /docs/ files in original
        docs_files = [f for f in original_files if '/docs/' in f]

        if not docs_files:
            print("  ✅ No /docs/ files to rename")
            continue

        print(f"  📄 Found {len(docs_files)} /docs/ files")

        # Verify they were renamed in actual_files
        for original_file in docs_files:
            expected_renamed = original_file.replace('/docs/', '/documents/')

            if expected_renamed in actual_files:
                print(f"  ✅ {original_file} → {expected_renamed}")

                # Verify file actually exists
                actual_path = repo_dir / expected_renamed.strip('/')
                if actual_path.exists():
                    print(f"    ✅ File exists at {actual_path}")
                else:
                    print(f"    ❌ File missing at {actual_path}")
                    all_tests_passed = False

                # Verify original doesn't exist
                original_path = repo_dir / original_file.strip('/')
                if not original_path.exists():
                    print(f"    ✅ Original file properly removed")
                else:
                    print(f"    ⚠️  Original file still exists at {original_path}")

            else:
                print(f"  ❌ {original_file} not properly renamed")
                all_tests_passed = False

    if all_tests_passed:
        print("\n🎉 All /docs/ → /documents/ renaming tests passed!")
    else:
        print("\n❌ Some renaming tests failed!")

    return all_tests_passed

if __name__ == "__main__":
    test_docs_rename()
