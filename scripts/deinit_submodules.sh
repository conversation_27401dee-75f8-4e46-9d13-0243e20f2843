#!/bin/bash
set -euo pipefail

echo "Deinitializing all submodules..."
git submodule deinit -f . || true

echo "Cleaning submodule sections from local Git config..."
git config --local -l | grep submodule | sed -E 's/^(submodule\.[^.]+).*/\1/' | sort -u | while read -r section; do
  echo "Trying to remove section [$section]..."
  git config --local --remove-section "$section" 2>/dev/null || echo "  (not found, skipped)"
done

echo "Removing .git/modules directory..."
rm -rf .git/modules

echo "Removing docs/external directory..."
rm -rf docs/external

echo "Syncing submodules..."
git submodule sync || true

echo "✅ Submodules cleaned."